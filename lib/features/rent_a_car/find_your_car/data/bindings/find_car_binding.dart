import 'package:get/get.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/data/data_sources/find_car_data_source_imp.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/data/data_sources/i_find_car_data_source.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/repositories/find_your_car_repository_imp.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/repositories/i_find_your_car_repository.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/use_cases/fetch_cars_use_case.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/use_cases/get_sort_options_use_case.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/presentation/manager/find_car_controller.dart';

class FindCarBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<FindLeaseCarDataSource>(
      () => FindLeaseCarDataSourceImplementation(
        getIt<ApiClient>(),
      ),
    );
    Get.lazyPut<FindLeaseCarRepository>(
      () => FindLeaseCarImplementation(
        findLeaseCarDataSource: Get.find<FindLeaseCarDataSource>(),
        networkInfo: getIt<NetworkInfo>(),
      ),
    );
    Get.lazyPut<GetAllLeaseVehiclesUseCase>(
      () => GetAllLeaseVehiclesUseCase(
        Get.find<FindLeaseCarRepository>(),
      ),
    );
    Get.lazyPut<GetSortOptionsUseCase>(
      () => GetSortOptionsUseCase(
        Get.find<FindLeaseCarRepository>(),
      ),
    );
    Get.lazyPut<FindCarController>(
      () => FindCarController(
        Get.find<GetAllLeaseVehiclesUseCase>(),
        Get.find<GetSortOptionsUseCase>(),
      ),
    );
  }
}
