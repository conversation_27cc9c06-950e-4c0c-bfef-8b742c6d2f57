import 'dart:ui';

import 'package:get/get.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';

class LanguageController extends GetxController {
  final UserSecureDataSource userSecureDataSource;
  final RxString currentLanguage = 'ar'.obs;

  LanguageController({
    required this.userSecureDataSource,
  });

  @override
  onInit() {
    super.onInit();
    getSavedLocal();
  }

  getSavedLocal() async {
    String? savedLanguage = await userSecureDataSource.getLanguage();
    if (savedLanguage == null) {
      currentLanguage.value = Get.deviceLocale?.languageCode ?? 'ar';
      await userSecureDataSource.setLanguage(currentLanguage.value);
    } else {
      currentLanguage.value = savedLanguage;
    }
  }

  Future<void> changeLanguage(String language) async {
    if (currentLanguage.value == language) return;

    try {
      // Update language in secure storage
      await userSecureDataSource.setLanguage(language);

      // Update GetX locale
      await Get.updateLocale(Locale(language));

      // Update current language
      currentLanguage.value = language;

      // Force app update to ensure all widgets are rebuilt
      await Get.forceAppUpdate();
    } catch (e) {
      print('Error changing language: $e');
      rethrow;
    }
  }
}
