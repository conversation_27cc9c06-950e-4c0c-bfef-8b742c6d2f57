import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio_intercept_to_curl/dio_intercept_to_curl.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/analytics/firebase_analytics_logger.dart';
import 'package:thrivve/core/analytics/multi_analytics_logger.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/core/app_client/auth_interceptor.dart';
import 'package:thrivve/core/deep_link/deep_link.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/proxy_service.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/click_drive_to_own_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_dashboard_new_products_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_info_thrivve_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_last_progress_use_case.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_work_with_uber_all_data_use_case.dart';
import 'package:thrivve/features/invoices/presentation/manager/invoices_bloc.dart';
import 'package:thrivve/features/myProductDetails/data/repositories/drive_to_own_repository_impl.dart';
import 'package:thrivve/features/myProductDetails/presentation/manager/drive_to_own_bloc.dart';
import 'package:thrivve/features/notifications/domain/use_cases/get_transaction_status_use_case.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/change_pin_use_case.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/verify_pin_use_case.dart';
import 'package:thrivve/features/referral/domain/use_cases/get_referral_status_use_case.dart';
import 'package:thrivve/features/settings/domain/use_cases/update_biometric_settings_use_case.dart';
import 'package:thrivve/features/splash/domain/use_cases/check_%20token_expiration_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/checkout_payment_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/get_bank_details_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/list_saved_cards_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/payment_methods_use_case.dart';
import 'package:thrivve/features/under_processing/data/data_sources/under_processing_data_source.dart';
import 'package:thrivve/features/under_processing/domain/repositories/under_processing_repository.dart';
import 'package:thrivve/features/under_processing/domain/repositories/under_processing_repository_impl.dart';
import 'package:thrivve/features/under_processing/domain/use_cases/get_list_under_processing_use_case.dart';
import 'package:thrivve/features/under_processing/presentation/manager/under_processing_bloc.dart';

import '../core/network/network_info.dart';
import '../features/bankAccount/data/data_sources/bank_remote_data_source.dart';
import '../features/bankAccount/data/repositories/add_bank_repository_impl.dart';
import '../features/bankAccount/domain/repositories/bank_repository.dart';
import '../features/bankAccount/domain/use_cases/add_bank_account_use_case.dart';
import '../features/bankAccount/domain/use_cases/delete_bank_use_case.dart';
import '../features/bankAccount/domain/use_cases/get_bank_details_use_case.dart';
import '../features/bankAccount/domain/use_cases/get_banks_use_case.dart';
import '../features/bankAccount/domain/use_cases/update_bank_account_use_case.dart';
import '../features/bankAccount/presentation/manager/bank_account_bloc.dart';
import '../features/dashboard/data/data_sources/dashboard_remote_data_source.dart';
import '../features/dashboard/data/repositories/dashboard_repository_impl.dart';
import '../features/dashboard/domain/repositories/dashboard_repository.dart';
import '../features/dashboard/domain/use_cases/change_language_use_case.dart';
import '../features/dashboard/domain/use_cases/delete_user_account_use_case.dart';
import '../features/dashboard/domain/use_cases/get_banners_use_case.dart';
import '../features/dashboard/domain/use_cases/get_insights_use_case.dart';
import '../features/dashboard/domain/use_cases/get_payment_methods_use_case.dart';
import '../features/dashboard/domain/use_cases/get_personal_info_use_case.dart';
import '../features/dashboard/domain/use_cases/get_products_use_case.dart';
import '../features/dashboard/domain/use_cases/get_support_url_use_case.dart';
import '../features/dashboard/domain/use_cases/get_sync_info_use_case.dart';
import '../features/dashboard/domain/use_cases/get_transactions_use_case.dart';
import '../features/dashboard/domain/use_cases/get_withdraw_balance_use_case.dart';
import '../features/dashboard/domain/use_cases/get_withdraw_config_use_case.dart';
import '../features/dashboard/domain/use_cases/get_withdraw_fees_use_case.dart';
import '../features/dashboard/domain/use_cases/home_page_message_use_case.dart';
import '../features/dashboard/domain/use_cases/set_sync_info_seen_use_case.dart';
import '../features/dashboard/presentation/manager/dashboard_bloc.dart';
import '../features/faqs/data/data_sources/faqs_remote_data_source.dart';
import '../features/faqs/data/repositories/faq_repository_impl.dart';
import '../features/faqs/domain/repositories/faq_repository.dart';
import '../features/faqs/domain/use_cases/get_faq_by_id_usecase.dart';
import '../features/faqs/domain/use_cases/get_faqs_usecase.dart';
import '../features/faqs/presentation/manager/faq_bloc.dart';
import '../features/identityInfo/data/repositories/identity_info_repository_impl.dart';
import '../features/identityInfo/domain/repositories/identity_info_repository.dart';
import '../features/identityInfo/domain/use_cases/get_identity_info_use_case.dart';
import '../features/identityInfo/domain/use_cases/save_identity_info_use_case.dart';
import '../features/identityInfo/presentation/manager/identity_info_bloc.dart';
import '../features/invoices/domain/use_cases/get_invoices_use_case.dart';
import '../features/invoices/domain/use_cases/get_list_of_invoices_filter_use_case.dart';
import '../features/listOfVehicles/data/data_sources/list_of_cars_remote_data_source.dart';
import '../features/listOfVehicles/data/repositories/list_of_cars_repository_impl.dart';
import '../features/listOfVehicles/domain/repositories/list_of_cars_repository.dart';
import '../features/listOfVehicles/domain/use_cases/get_list_of_filter_use_case.dart';
import '../features/listOfVehicles/domain/use_cases/get_list_of_vehicles_use_case.dart';
import '../features/listOfVehicles/presentation/manager/list_of_vehicles_bloc.dart';
import '../features/mainHome/presentation/manager/main_home_bloc.dart';
import '../features/myProductDetails/data/data_sources/drive_to_own_remote_data_source.dart';
import '../features/myProductDetails/domain/repositories/drive_to_own_repository.dart';
import '../features/myProductDetails/domain/use_cases/get_contract_details_data_use_case.dart';
import '../features/myProductDetails/domain/use_cases/get_drive_to_own_attachment_use_case.dart';
import '../features/myProductDetails/domain/use_cases/get_drive_to_own_data_use_case.dart';
import '../features/myProductDetails/domain/use_cases/get_drive_to_own_installments_use_case.dart';
import '../features/myProductDetails/domain/use_cases/get_list_of_rented_contracts_use_case.dart';
import '../features/myProductDetails/domain/use_cases/report_incident_use_case.dart';
import '../features/my_products/presentation/manager/my_products_bloc.dart';
import '../features/notifications/data/data_sources/notification_remote_data_source.dart';
import '../features/notifications/data/repositories/notification_repository_impl.dart';
import '../features/notifications/domain/repositories/notification_repository.dart';
import '../features/notifications/domain/use_cases/get_notifications_use_case.dart';
import '../features/notifications/domain/use_cases/get_unseen_notification_use_case.dart';
import '../features/notifications/domain/use_cases/set_notification_seen_use_case.dart';
import '../features/notifications/presentation/manager/notification_bloc.dart';
import '../features/onboarding/data/data_sources/auth_remote_data_source.dart';
import '../features/onboarding/data/repositories/auth_repository_impl.dart';
import '../features/onboarding/domain/repositories/auth_repository.dart';
import '../features/onboarding/domain/use_cases/get_countries_use_case.dart';
import '../features/onboarding/domain/use_cases/get_current_country_code_use_case.dart';
import '../features/onboarding/domain/use_cases/get_flags_use_case.dart';
import '../features/onboarding/domain/use_cases/logout_use_case.dart';
import '../features/onboarding/domain/use_cases/send_otp_use_case.dart';
import '../features/onboarding/domain/use_cases/verify_otp_use_case.dart';
import '../features/onboarding/presentation/manager/onboarding_bloc.dart';
import '../features/personalInfo/data/data_sources/personal_info_and_identity_remote_data_source.dart';
import '../features/personalInfo/data/repositories/personal_info_repository_impl.dart';
import '../features/personalInfo/domain/repositories/personal_info_repository.dart';
import '../features/personalInfo/domain/use_cases/get_cities_of_country_use_case.dart';
import '../features/personalInfo/domain/use_cases/get_user_info_use_case.dart';
import '../features/personalInfo/domain/use_cases/save_user_info_use_case.dart';
import '../features/personalInfo/domain/use_cases/upload_image_use_case.dart';
import '../features/personalInfo/presentation/manager/personal_info_bloc.dart';
import '../features/pin/domain/use_cases/sing_in_use_case.dart';
import '../features/pin/presentation/manager/pin_bloc.dart';
import '../features/products/presentation/manager/products_bloc.dart';
import '../features/referral/data/data_sources/referral_remote_data_source.dart';
import '../features/referral/data/repositories/referral_repository_impl.dart';
import '../features/referral/domain/repositories/referral_repository.dart';
import '../features/referral/domain/use_cases/get_referral_status_values_use_case.dart';
import '../features/referral/presentation/manager/referral_bloc.dart';
import '../features/settings/data/data_sources/profile_remote_data_source.dart';
import '../features/settings/data/repositories/profile_repository_impl.dart';
import '../features/settings/domain/repositories/profile_repository.dart';
import '../features/settings/domain/use_cases/send_otp_for_change_phone_use_case.dart';
import '../features/settings/domain/use_cases/verify_otp_for_change_phone_use_case.dart';
import '../features/settings/presentation/manager/updateMobile/update_mobile_bloc.dart';
import '../features/splash/presentation/manager/splash_bloc.dart';
import '../features/topUp/data/data_sources/top_up_remote_data_source.dart';
import '../features/topUp/data/repositories/top_up_repository_impl.dart';
import '../features/topUp/domain/repositories/top_up_repository.dart';
import '../features/topUp/domain/use_cases/get_top_up_creation_data_use_case.dart';
import '../features/topUp/domain/use_cases/make_top_up_request_use_case.dart';
import '../features/topUp/presentation/manager/top_up_bloc.dart';
import '../features/transactionDetails/domain/use_cases/cancel_transaction_use_case.dart';
import '../features/transactionDetails/domain/use_cases/get_transaction_details_use_case.dart';
import '../features/transactionDetails/presentation/manager/transaction_details_bloc.dart';
import '../features/transactions/domain/use_cases/get_list_of_transactions_filter_use_case.dart';
import '../features/transactions/presentation/manager/transaction_bloc.dart';
import '../features/vehicleDetails/domain/use_cases/apply_as_a_lead_use_case.dart';
import '../features/vehicleDetails/domain/use_cases/get_contact_us_links_use_case.dart';
import '../features/vehicleDetails/domain/use_cases/get_list_of_city_use_case.dart';
import '../features/vehicleDetails/domain/use_cases/get_list_of_country_use_case.dart';
import '../features/vehicleDetails/domain/use_cases/get_vehcile_details_use_case.dart';
import '../features/vehicleDetails/domain/use_cases/send_drive_to_own_or_rent_use_case.dart';
import '../features/vehicleDetails/presentation/manager/vehicle_details_bloc.dart';
import '../features/withdraw/data/data_sources/withdraw_remote_data_source.dart';
import '../features/withdraw/data/repositories/withdraw_repository_impl.dart';
import '../features/withdraw/domain/repositories/withdraw_repository.dart';
import '../features/withdraw/domain/use_cases/check_withdraw_fees_use_case.dart';
import '../features/withdraw/domain/use_cases/get_balance_creation_details_use_case.dart';
import '../features/withdraw/domain/use_cases/get_onboarding_settings_use_case.dart';
import '../features/withdraw/domain/use_cases/get_payment_method_creation_setting_use_case.dart';
import '../features/withdraw/domain/use_cases/make_withdraw_request_use_case.dart';
import '../features/withdraw/presentation/manager/withdraw_bloc.dart';
import 'localDataSource/user_secure_data_source_impl.dart';

GetIt getIt = GetIt.instance;

Future<void> init({bool enableProxy = false, bool testMode = false}) async {
  // External
  _injectStorage();
  initExternal(enableProxy: enableProxy, isTestMode: testMode);
  // init Core
  initCore();
  // init features
  initFeatures();
  getIt.registerSingleton<SharedPreferences>(
      await SharedPreferences.getInstance());
}

Future<void> setupProxy(dio, {bool enableProxy = false}) async {
  if (enableProxy) {
    // Retrieve the system proxy settings
    final proxy = await ProxyService.getSystemProxy();
    print("system-proxy is $proxy");

    if (proxy != null) {
      final adapter = dio.httpClientAdapter as IOHttpClientAdapter;

      adapter.createHttpClient = () {
        return HttpClient()
          ..findProxy = (uri) {
            return "PROXY $proxy";
          }
          ..badCertificateCallback =
              (X509Certificate cert, String host, int port) => true;
      };
    }
  }
}

void initExternal({bool enableProxy = false, bool isTestMode = false}) {
  getIt.registerLazySingleton<Dio>(() {
    final Dio dio = Dio();

    dio.interceptors.add(
      DioInterceptToCurl(
        printOnSuccess: true,
      ),
    );
    dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
      ),
    );
    dio.interceptors.add(
      AuthInterceptor(
        dio,
        getIt<UserSecureDataSource>(),
      ),
    );
    setupProxy(dio, enableProxy: enableProxy);

    // Forward the requests though the system-proxy in debugMode

    return dio;
  });
  getIt.registerLazySingleton<InternetConnectionChecker>(
    () => InternetConnectionChecker.createInstance(),
  );

  getIt.registerLazySingleton<FirebaseAnalytics>(
      () => FirebaseAnalytics.instance);
}

void initCore() {
  getIt.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(getIt()));

  Get.put<ThemeController>(
    ThemeController(getIt()),
    permanent: true,
  );

  getIt.registerLazySingleton<DeepLink>(
    () => DeepLink(
      userSecureDataSource: getIt(),
      analyticsLogger: getIt(),
    ),
  );

  getIt.registerLazySingleton<ApiClient>(
    () => ApiClient(
      dio: getIt(),
      userSecureDataSource: getIt(),
    ),
  );
}

void _injectStorage() {
  final secureStorage = FlutterSecureStorage(
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock,
    ),
  );
  getIt.registerLazySingleton<UserSecureDataSource>(
    () => UserSecureDataSourceImpl(secureStorage: secureStorage),
  );
}

void initFeatures() {
  getIt.registerLazySingleton<IAnalyticsLogger>(
    () => MultiAnalyticsLogger(
      [
        FirebaseAnalyticsLogger(),
      ],
      getIt(),
    ),
  );
  getIt.registerFactory(() => ListOfVehiclesBloc(
        getListOfVehiclesUseCase: getIt(),
        getListOfFilterUseCase: getIt(),
        userSecureDataSource: getIt(),
      ));

  getIt.registerFactory(() => InvoicesBloc(
        getInvoicesUseCase: getIt(),
        getListOfInvoicesFilterUseCase: getIt(),
      ));

  getIt.registerLazySingleton(() => ReferralBloc(
        getReferralStatusValues: getIt(),
      ));

  getIt.registerFactory(
    () => VehicleDetailsBloc(
      sendDriveToOwnOrRentUseCase: getIt(),
      userSecureDataSource: getIt(),
      getCountriesUseCase: getIt(),
      sendOtpUseCase: getIt(),
      verifyOtpUseCase: getIt(),
      getCurrentCountryCodeUseCase: getIt(),
      applyAsALeadUseCase: getIt(),
      getVehicleDetailsUseCase: getIt(),
      getContactUsLinksUseCase: getIt(),
      getSupportUrlUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => PinBloc(
      changePinUseCase: getIt(),
      verifyPinUseCase: getIt(),
      signInUseCase: getIt(),
      userSecureDataSource: getIt(),
      sendOtpUseCase: getIt(),
      verifyOtpUseCase: getIt(),
      getSupportUrlUseCase: getIt(),
    ),
  );

  getIt.registerLazySingleton(
    () => MainHomeBloc(
      getFlagsUseCase: getIt(),
      getWithdrawBalanceUseCase: getIt(),
      changeLanguageUseCase: getIt(),
      deleteUserAccountUseCase: getIt(),
      getUnSeenNotificationUseCase: getIt(),
      getPersonalInfoUseCase: getIt(),
      getDriveToOwnDataUseCase: getIt(),
      userSecureDataSource: getIt(),
      getSupportUrlUseCase: getIt(),
      getReferralStatusUseCase: getIt(),
      saveUserInfoUseCase: getIt(),
      updateBiometricSettingsUseCase: getIt(),
      infoSupplierUseCase: getIt(),
    ),
  );
  getIt.resetLazySingleton<MainHomeBloc>(
      disposingFunction: (bloc) => bloc.close());

  getIt.registerFactory(
    () => PersonalInfoBloc(
      saveUserInfoUseCase: getIt(),
      getUserInfoUseCase: getIt(),
      getCitiesOfCountryUseCase: getIt(),
    ),
  );
  getIt.registerFactory(() => IdentityInfoBloc(
        getIdentityInfoUseCase: getIt(),
        saveUserInfoUseCase: getIt(),
        setNotificationSeenUseCase: getIt(),
      ));

  getIt.registerFactory(
    () => TopUpBloc(
      makeTopUpRequestUseCase: getIt(),
      getThrivveBankDetailsDataUseCase: getIt(),
      paymentMethodsUseCase: getIt(),
      checkoutPaymentUseCase: getIt(),
      listSavedCardsUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => SplashBloc(
      userSecureDataSource: getIt(),
      checkTokenExpirationUseCase: getIt(),
    ),
  );
  getIt.registerFactory(
    () => BankAccountBloc(
      addBankAccountUseCase: getIt(),
      getBanksUseCase: getIt(),
      deleteBankUseCase: getIt(),
      getPaymentMethodsUseCase: getIt(),
      getBankDetailsUseCase: getIt(),
      updateBankAccountUseCase: getIt(),
      getPaymentMethodCreationSettingUseCase: getIt(),
    ),
  );
  getIt.registerLazySingleton(
    () => DashboardBloc(
      getTransactionsUseCase: getIt(),
      getInsightsUseCase: getIt(),
      getProductsUseCase: getIt(),
      userSecureDataSource: getIt(),
      getSyncInfoUseCase: getIt(),
      setSyncInfoSeenUseCase: getIt(),
      getOnboardingSettingUseCase: getIt(),
      getTopUpCreationDataUseCase: getIt(),
      getHomePageMessageUseCase: getIt(),
      getListUnderProcessingUseCase: getIt(),
      getWorkWithUberAllDataUseCase: getIt(),
      getLastProgressUseCase: getIt(),
      getDashboardNewProductsUseCase: getIt(),
      clickDriveToOwnUseCase: getIt(),
    ),
  );
  getIt.registerFactory(
    () => NotificationBloc(
      getNotificationUseCase: getIt(),
      setNotificationSeenUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => OnboardingBloc(
      getFlagsUseCase: getIt(),
      sendOtpUseCase: getIt(),
      verifyOtpUseCase: getIt(),
      getCountriesUseCase: getIt(),
      getCurrentCountryCodeUseCase: getIt(),
      userSecureDataSource: getIt(),
      getSupportUrlUseCase: getIt(),
      signInUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => DriveToOwnBloc(
      getDriveToOwnDataUseCase: getIt(),
      getDriveToOwnDataAttachmentUseCase: getIt(),
      getDriveToOwnDataInstallmentsUseCase: getIt(),
      reportIncidentUseCase: getIt(),
      getContractDetailsDataUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => TransactionBloc(
      getTransactionsUseCase: getIt(),
      userSecureDataSource: getIt(),
      getListOfTransactionsFilterUseCase: getIt(),
    ),
  );

  getIt.registerFactory(
    () => MyProductsBloc(getListOfRentedContractUseCase: getIt()),
  );

  // ProductsBloc
  getIt.registerFactory(() => ProductsBloc(getProductsUseCase: getIt()));

  // faq feature
  // Bloc
  getIt.registerFactory<FaqBloc>(() => FaqBloc(
        getFaqsUseCase: getIt(),
        getFaqByIdUseCase: getIt(),
      ));

  // WithdrawBloc
  getIt.registerLazySingleton<WithdrawBloc>(() => WithdrawBloc(
        checkWithdrawFeesUseCase: getIt(),
        getPayoutMethodsUseCase: getIt(),
        addBankAccountUseCase: getIt(),
        makeWithdrawRequestUseCase: getIt(),
        getPublicBanksUseCase: getIt(),
        getBalanceCreationDetailsUseCase: getIt(),
        getPaymentMethodCreationSettingUseCase: getIt(),
        userSecureDataSource: getIt(),
        getOnboardingSettingUseCase: getIt(),
      ));
  getIt.registerFactory<TransactionDetailsBloc>(() => TransactionDetailsBloc(
        getTransactionDetailsUseCase: getIt(),
        cancelTransactionUseCase: getIt(),
      ));

  getIt.registerFactory<UpdateMobileBloc>(() => UpdateMobileBloc(
      sendOtpForChangePhoneUseCase: getIt(),
      verifyOtpForChangePhoneUseCase: getIt(),
      getCountriesUseCase: getIt(),
      getCurrentCountryCodeUseCase: getIt(),
      userSecureDataSource: getIt()));

  // Use Cases
  //CheckWithdrawFeesUseCase
  getIt.registerLazySingleton<SendOtpForChangePhoneUseCase>(
      () => SendOtpForChangePhoneUseCase(profileRepository: getIt()));
  getIt.registerLazySingleton<UpdateBiometricSettingsUseCase>(
      () => UpdateBiometricSettingsUseCase(getIt()));
  getIt.registerLazySingleton<VerifyOtpForChangePhoneUseCase>(
      () => VerifyOtpForChangePhoneUseCase(profileRepository: getIt()));
  getIt.registerLazySingleton<GetOnboardingSettingUseCase>(
      () => GetOnboardingSettingUseCase(repository: getIt()));
  getIt.registerLazySingleton<CancelTransactionUseCase>(
      () => CancelTransactionUseCase(repository: getIt()));
  getIt.registerLazySingleton<GetTransactionDetailsUseCase>(
      () => GetTransactionDetailsUseCase(repository: getIt()));
  getIt.registerLazySingleton<CheckWithdrawFeesUseCase>(
      () => CheckWithdrawFeesUseCase(repository: getIt()));
  //MakeWithdrawRequestUseCase
  getIt.registerLazySingleton<MakeWithdrawRequestUseCase>(
      () => MakeWithdrawRequestUseCase(repository: getIt()));
  getIt.registerLazySingleton<GetFaqsUseCase>(
      () => GetFaqsUseCase(repository: getIt()));
  getIt.registerLazySingleton<GetFaqByIdUseCase>(
      () => GetFaqByIdUseCase(repository: getIt()));
  //GetTopUpCreationDataUseCase
  getIt.registerLazySingleton<GetTopUpCreationDataUseCase>(
      () => GetTopUpCreationDataUseCase(repository: getIt()));
  getIt.registerLazySingleton<CheckoutPaymentUseCase>(
      () => CheckoutPaymentUseCase(repository: getIt()));

  getIt.registerLazySingleton<PaymentMethodsUseCase>(
      () => PaymentMethodsUseCase(repository: getIt()));
  getIt.registerLazySingleton<ListSavedCardsUseCase>(
      () => ListSavedCardsUseCase(repository: getIt()));

  //MakeTopUpRequestUseCase
  getIt.registerLazySingleton<MakeTopUpRequestUseCase>(
      () => MakeTopUpRequestUseCase(repository: getIt()));
  // get bank details
  getIt.registerLazySingleton<GetThrivveBankDetailsDataUseCase>(
      () => GetThrivveBankDetailsDataUseCase(repository: getIt()));

  // Repository
  getIt.registerLazySingleton<FaqRepository>(
      () => FaqRepositoryImpl(remoteDataSource: getIt(), networkInfo: getIt()));

  //TopUpRepository
  getIt.registerLazySingleton<TopUpRepository>(() =>
      TopUpRepositoryImpl(remoteDataSource: getIt(), networkInfo: getIt()));
  // Data Sources
  getIt
      .registerLazySingleton<FaqRemoteDataSource>(() => FaqRemoteDataSourceImpl(
            apiClient: getIt(),
          ));

  getIt.registerLazySingleton<TopUpRemoteDataSource>(
      () => TopUpRemoteDataSourceImpl(
            apiClient: getIt(),
          ));

  // use cases

  getIt.registerLazySingleton(
      () => GetReferralStatusUseCase(repository: getIt()));
  getIt.registerLazySingleton(
      () => GetBalanceCreationDetailsUseCase(repository: getIt()));
  getIt.registerLazySingleton(
      () => GetPaymentMethodCreationSettingUseCase(repository: getIt()));

  getIt.registerLazySingleton(
      () => GetReferralStatusValuesUseCase(repository: getIt()));

  getIt.registerLazySingleton(
      () => GetSyncInfoUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => SetSyncInfoSeenUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
    () => HomePageMessageUseCase(
      dashboardRepository: getIt(),
    ),
  );
  getIt.registerLazySingleton(
    () => ClickDriveToOwnUseCase(
      dashboardRepository: getIt(),
    ),
  );

  getIt
      .registerLazySingleton(() => GetListOfTransactionsFilterUseCase(getIt()));
  getIt.registerLazySingleton(
      () => GetProductsUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(() => GetListOfVehiclesUseCase(getIt()));
  getIt.registerLazySingleton(() => GetListOfFilterUseCase(getIt()));
  getIt.registerLazySingleton(() => GetListOfInvoicesFilterUseCase(getIt()));
  getIt.registerLazySingleton(() => GetContactUsLinksUseCase(getIt()));
  getIt.registerLazySingleton(() => GetVehicleDetailsUseCase(getIt()));
  getIt.registerLazySingleton(
      () => CheckTokenExpirationUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => GetFlagsUseCase(authRepository: getIt()));

  getIt.registerLazySingleton(
      () => GetInvoicesUseCase(dashboardRepository: getIt()));

  getIt.registerLazySingleton(
      () => ReportIncidentUseCase(driveToOwnRepository: getIt()));

  getIt.registerLazySingleton(
      () => GetListOfRentedContractUseCase(driveToOwnRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetDriveToOwnDataUseCase(driveToOwnRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetDriveToOwnDataAttachmentUseCase(driveToOwnRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetContractDetailsDataUseCase(driveToOwnRepository: getIt()));
  getIt.registerLazySingleton(() =>
      GetDriveToOwnDataInstallmentsUseCase(driveToOwnRepository: getIt()));

  getIt.registerLazySingleton(() => ApplyAsALeadUseCase(getIt()));
  getIt.registerLazySingleton(() => SendDriveToOwnOrRentUseCase(getIt()));
  getIt.registerLazySingleton(() => GetListOfCountryUserCase(getIt()));
  getIt.registerLazySingleton(() => GetListOfCityUserCase(getIt()));

  getIt.registerLazySingleton(
      () => ChangeLanguageUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => DeleteUserAccountUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetUnSeenNotificationUseCase(notificationRepository: getIt()));
  getIt.registerLazySingleton(
      () => SetNotificationSeenUseCase(notificationRepository: getIt()));
  getIt.registerLazySingleton(() => SendOtpUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => SignInUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => VerifyOtpUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => LogoutUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => VerifyPinUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => ChangePinUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetCurrentCountryCodeUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetCountriesUseCase(authRepository: getIt()));
  getIt.registerLazySingleton(() => GetWithdrawBalanceUseCase(getIt()));
  getIt.registerLazySingleton(() => GetCitiesOfCountryUseCase(getIt()));
  getIt.registerLazySingleton(() => SaveIdentityInfoUseCase(getIt()));
  getIt.registerLazySingleton(() => GetIdentityInfoUseCase(getIt()));
  getIt.registerLazySingleton(() => UploadImageUseCase(getIt()));
  getIt.registerLazySingleton(() => SaveUserInfoUseCase(getIt()));
  getIt.registerLazySingleton(() => GetUserInfoUseCase(getIt()));
  getIt.registerLazySingleton(
      () => GetPersonalInfoUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetPaymentMethodsUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetWorkWithUberAllDataUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetLastProgressUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetSupportUrlUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetInfoThrivveUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetWithdrawFeesUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetWithdrawConfigUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetInsightsUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetBannersUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetDashboardNewProductsUseCase(dashboardRepository: getIt()));

  getIt.registerLazySingleton(
      () => GetTransactionsUseCase(dashboardRepository: getIt()));
  getIt.registerLazySingleton(
      () => AddBankAccountUseCase(bankRepository: getIt()));
  getIt.registerLazySingleton(
      () => UpdateBankAccountUseCase(bankRepository: getIt()));
  getIt.registerLazySingleton(() => GetBanksUseCase(bankRepository: getIt()));
  getIt.registerLazySingleton(() => DeleteBankUseCase(bankRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetBankDetailsUseCase(bankRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetNotificationUseCase(notificationRepository: getIt()));
  getIt.registerLazySingleton(
      () => GetTransctionStatusUseCase(notificationRepository: getIt()));

  // repositories

  // referral repository
  getIt.registerLazySingleton<WithdrawRepository>(
    () => WithdrawRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<ReferralRepository>(
    () => ReferralRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<ListOfCarsRepository>(
    () => ListOfCarsRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
      localDataSource: getIt(),
    ),
  );
  getIt.registerLazySingleton<DriveToOwnRepository>(
    () => DriveToOwnRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<BankRepository>(
    () => BankRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<PersonalInfoRepository>(
    () => PersonalInfoRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<IdentityInfoRepository>(
    () => IdentityInfoRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton<NotificationRepository>(
    () => NotificationRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );

  getIt.registerLazySingleton<ProfileRepository>(
    () => ProfileRepositoryImpl(
      remoteDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  // Data Sources

  // referral data source
  getIt.registerLazySingleton<ReferralRemoteDataSource>(
    () => ReferralRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );

  getIt.registerLazySingleton<ProfileRemoteDataSource>(
    () => ProfileRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );

  //WithdrawRemoteDataSource
  getIt.registerLazySingleton<WithdrawRemoteDataSource>(
    () => WithdrawRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );

  getIt.registerLazySingleton<ListOfCarsRemoteDataSource>(
    () => ListOfCarsRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<DriveToOwnRemoteDataSource>(
    () => DriveToOwnRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<PersonalAndIdentityInfoRemoteDataSource>(
    () => PersonalAndIdentityInfoRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );

  getIt.registerLazySingleton<BankRemoteDataSource>(
    () => BankRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<NotificationRemoteDataSource>(
    () => NotificationRemoteDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  injectUnderProcessingFeature();
}

void injectUnderProcessingFeature() {
  getIt.registerLazySingleton<UnderProcessingDataSource>(
    () => UnderProcessingDataSourceImpl(
      apiClient: getIt(),
    ),
  );
  getIt.registerLazySingleton<UnderProcessingRepository>(
    () => UnderProcessingRepositoryImpl(
      underProcessingDataSource: getIt(),
      networkInfo: getIt(),
    ),
  );
  getIt.registerLazySingleton(
    () => GetListUnderProcessingUseCase(
      underProcessingRepositoryImpl: getIt(),
    ),
  );

  getIt.registerFactory(
    () => UnderProcessingBloc(
      getListUnderProcessingUseCase: getIt(),
    ),
  );
}
