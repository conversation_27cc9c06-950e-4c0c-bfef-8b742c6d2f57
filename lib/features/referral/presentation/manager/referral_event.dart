// lib/presentation/bloc/referral_event.dart
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';

import '../../domain/entities/referral_status.dart';

@immutable
abstract class ReferralEvent extends Equatable {
  const ReferralEvent();

  @override
  List<Object?> get props => [];
}

class FetchReferralStatus extends ReferralEvent {
  final ReferralStatus? referralStatus;

  const FetchReferralStatus({required this.referralStatus});

  @override
  List<Object?> get props => [referralStatus];
}
