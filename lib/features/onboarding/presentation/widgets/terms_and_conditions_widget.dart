import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/const.dart';

import '../../../../core/api/api_settings.dart';
import '../../../../core/widget/web_page_viewer_bottom_sheet.dart';

class TermsAndConditionsWidget extends StatelessWidget {
  const TermsAndConditionsWidget({
    super.key,
    this.isLogin = false,
  });
  final bool isLogin;
  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(
        style: TextStyle(
          color: context.black.withValues(alpha: 0.7),
          fontSize: 11.sp,
          fontWeight: FontWeight.w400,
          fontFamily: Get.locale?.languageCode == valueArLanguage
              ? "NotoSansArabic"
              : "NotoSans",
        ),
        children: [
          TextSpan(
            text: isLogin
                ? "term_and_conditions_msg".tr
                : "term_and_conditions_msg_register".tr,
          ),
          TextSpan(
            text: 'term_msg'.tr,
            style: TextStyle(
              color: context.outlineButtonColor,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.underline,
              decorationStyle: TextDecorationStyle.dotted,
              fontSize: 11.sp,
              fontFamily: Get.locale?.languageCode == valueArLanguage
                  ? "NotoSansArabic"
                  : "NotoSans",
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                openTheUriInWebPage(
                  url: ApiSettings.termAndConditionsUrl +
                      (Get.locale?.languageCode == valueArLanguage
                          ? "ar"
                          : "en"),
                  context: context,
                );
              },
          ),
          TextSpan(
            text: "and".tr,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11.sp,
              fontFamily: Get.locale?.languageCode == valueArLanguage
                  ? "NotoSansArabic"
                  : "NotoSans",
              color: context.black.withValues(alpha: 0.70),
            ),
          ),
          TextSpan(
            text: "privacy_msg".tr,
            style: TextStyle(
              color: context.outlineButtonColor,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.underline,
              decorationStyle: TextDecorationStyle.dotted,
              fontFamily: Get.locale?.languageCode == valueArLanguage
                  ? "NotoSansArabic"
                  : "NotoSans",
              fontSize: 11.sp,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                openTheUriInWebPage(
                  url: ApiSettings.privacyPolicyUrl,
                  context: context,
                );
              },
          ),
        ],
      ),
    );
  }

  void openTheUriInWebPage({String? url, required BuildContext context}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return Container(
          margin: EdgeInsets.only(top: 44.h),
          child: WebPageViewerBottomSheet(
            url: url,
          ),
        );
      },
    );
  }
}
