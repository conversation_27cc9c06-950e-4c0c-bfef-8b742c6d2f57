import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// when open any screen includes this parent widget, should use:
/// opaque = true
/// fullscreenDialog: true
///
/// ex.
/// GetPage<void>(
///   name: Routes.clientInformation,
///   page: () => Page(),
///    fullscreenDialog: true,
///    opaque: false,
///  ),
class ScreenAsBottomSheet extends StatelessWidget {
  const ScreenAsBottomSheet({
    Key? key,
    required this.dismissibleKey,
    required this.child,
    this.onDismissUpdated,
    this.bottomSafeArea = true,
    this.enabledDrag = true,
  }) : super(key: key);

  final Widget child;
  final String dismissibleKey;
  final bool bottomSafeArea;
  final bool enabledDrag;
  final void Function(DismissUpdateDetails)? onDismissUpdated;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: bottomSafeArea,
      maintainBottomViewPadding: true,
      child: Visibility(
        visible: enabledDrag,
        replacement: bodyContent(),
        child: Dismissible(
          key: Key(dismissibleKey),
          direction: DismissDirection.down,
          onDismissed: (_) => Get.back<void>(),
          onUpdate: onDismissUpdated,
          child: bodyContent(),
        ),
      ),
    );
  }

  Widget bodyContent() {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: Container(child: child),
    );
  }
}
