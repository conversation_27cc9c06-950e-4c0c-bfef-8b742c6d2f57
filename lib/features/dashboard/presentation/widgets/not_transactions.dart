import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class NotTransactions extends StatelessWidget {
  const NotTransactions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          height: 16,
        ),
        Container(
          padding: const EdgeInsets.all(25),
          decoration: BoxDecoration(
              color: context.iconAndBtnBackground, shape: BoxShape.circle),
          child: Image.asset(
            Assets.thrivvePhotosNoTransactions,
            width: 150,
            height: 150,
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        Text(
          "no_transactions_history".tr,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: context.statusBackground,
          ),
        )
      ],
    );
  }
}
