import 'package:flutter/material.dart';
import 'package:flutter_pagewise/flutter_pagewise.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/upload_image/custom_file_upload_widget.dart';
import 'package:thrivve/core/upload_image/file_upload_controller.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_drop_down_list_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/listOfVehicles/domain/entities/vehicle_list_object.dart';
import 'package:thrivve/features/topUp/presentation/widgets/notes_with_bold_widget.dart';
import 'package:thrivve/features/uber_flow/instructions_uber_flow/presentation/arguments/instruction_uber_flow_arguments.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/vehicle_model_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/manager/select_vehicles_uber_flow_controller.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/widgets/vehicle_item_widget.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/back_icon.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/indicators_widget.dart';

class SelectVehiclesUberFlowPage
    extends GetView<SelectVehiclesUberFlowController> {
  const SelectVehiclesUberFlowPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: _onPopInvoked,
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              _header(context),
              IndicatorsWidget(1).paddingSymmetric(horizontal: 20.w),
              Expanded(
                child: SingleChildScrollView(
                  controller: controller.controller,
                  child: Column(
                    children: [
                      _title(context),
                      SizedBox(height: 40.h),
                      _dropDownListSelection(context),
                      _dropDownListModelSelection(context),
                      SizedBox(height: 40.h),
                      _titleSelection(),
                      SizedBox(height: 10.h),
                      _buildContentSection(context),
                    ],
                  ),
                ),
              ),
              // Note at bottom
              _note(context),
              SizedBox(height: 40.sp),
              _divider(context),
              SizedBox(height: 13.sp),
              _button(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _header(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          BackIcon(
            removeSpacing: true,
            onClickBack: _onClickBack,
          ),
        ],
      ),
    );
  }

  Widget _title(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Flexible(
          child: CustomTextWidget(
            paddingStart: 16.w,
            paddingEnd: 16.w,
            title: 'add_or_choose_vehicle'.tr,
            size: 22,
            color: context.black,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  Widget _dropDownListSelection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() => Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextWidget(
                title: 'do_you_want_use_own_car'.tr,
                paddingBottom: 8.h,
                size: 12,
                color: context.black,
              ),
              CustomDropdownBox<String>(
                key: ValueKey("vehicle_drop_down"),
                hint: '',
                value: controller.selectedOption,
                onChanged: controller.setSelectedOption,
                items: [
                  DropdownItem<String>(
                    value: 'rent',
                    label: 'leasing_car'.tr,
                    icon: Icons.car_rental,
                  ),
                  DropdownItem<String>(
                    value: 'own',
                    label: 'own_car'.tr,
                    icon: Icons.directions_car,
                  ),
                ],
              ),
            ],
          )),
    );
  }

  Widget _dropDownListModelSelection(BuildContext context) {
    return Obx(() => Visibility(
          replacement: SizedBox.shrink(),
          visible: controller.selectedOption != null &&
              controller.selectedOption != 'rent',
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 40.h),
                if (controller.modelsVehicle.isNotEmpty)
                  CustomTextWidget(
                    title: 'car_model_title'.tr,
                    paddingBottom: 8.h,
                    size: 12,
                    color: context.black,
                  ),
                if (controller.modelsVehicle.isNotEmpty)
                  CustomDropdownBox<VehicleModelEntity?>(
                    hint: 'select_car_model'.tr,
                    value: controller.modelVehicleSelection.value,
                    onChanged: (VehicleModelEntity? item) {
                      controller.modelVehicleSelection.value = item;
                    },
                    items: controller.modelsVehicle
                        .map(
                          (VehicleModelEntity? e) =>
                              DropdownItem<VehicleModelEntity?>(
                            value: e,
                            label: e?.title ?? '',
                            icon: Icons.car_repair_sharp,
                          ),
                        )
                        .toList(),
                  )
              ],
            ),
          ),
        ));
  }

  Widget _buildContentSection(BuildContext context) {
    return Obx(() {
      return Visibility(
        visible: controller.selectedOption == null,
        replacement: Visibility(
          visible: controller.selectedOption == 'own',
          replacement: _buildVehiclePageView(),
          child: Visibility(
            visible: isShowDocsUploader,
            replacement: SizedBox.shrink(),
            child: _buildDocumentUploadSection(context),
          ),
        ),
        child: SizedBox.shrink(),
      );
    });
  }

  Widget _buildDocumentUploadSection(BuildContext context) {
    return Column(
      children: [
        // Main content

        Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          child: CustomFileUploadWidget(
            borderColor: context.black.withValues(alpha: 0.2),
            controllerId: 'select_vehicle',
            initialFileUrl: controller.selectedDocument,
            isFile: true,
            onFileDeleted: () {
              controller.deleteImageOwnVehicles();
            },
            onFileUploaded: (fileUrl) {
              controller.selectedDocument = fileUrl ?? '';
            },
            onFileSelected: (filePath) {
              Get.back();
              if (filePath?.isNotEmpty == true) {
                if (filePath != null) {
                  Get.find<FileUploadController>(tag: 'select_vehicle')
                      .uploadFile(filePath);
                }
              }
            },
            width: double.infinity,
            height: 140.h,
            title: "upload_file_here".tr,
            description: "supported_formats".tr,
          ),
        ),
      ],
    );
  }

  Widget _buildVehiclePageView() {
    final pageController = controller.vehiclesPageLoaderController;
    if (pageController == null) {
      return const SizedBox.expand();
    }
    return PagewiseListView<VehicleListObject>(
      pageLoadController: controller.vehiclesPageLoaderController,
      itemBuilder: _itemBuilderVehicles,
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      loadingBuilder: _loadingBuilder,
    );
  }

  Widget _loadingBuilder(BuildContext context) {
    return ListView.builder(
        itemCount: 4,
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          return Shimmer.fromColors(
            baseColor: context.borderAddBranch,
            highlightColor: context.appBackgroundColor,
            child: _itemBuilderVehicles(
              context,
              VehicleListObject(),
              0,
            ),
          );
        });
  }

  Widget _itemBuilderVehicles(
      BuildContext context, VehicleListObject vehicle, int index) {
    return Obx(
      () => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => controller.onClickItemRent(vehicle),
        child: VehicleItemWidget(
          vehicle: vehicle,
          vehiclesSelectionId: controller.vehicleItemSelection?.id,
        ),
      ),
    );
  }

  Widget _titleSelection() {
    return Obx(
      () => Visibility(
        visible: controller.selectedOption != null,
        replacement: SizedBox.shrink(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CustomTextWidget(
              size: 12,
              paddingStart: 16.w,
              paddingEnd: 16.w,
              title: controller.selectedOption == 'own'
                  ? isShowDocsUploader
                      ? 'upload_vehicle_registration_documents'.tr
                      : ''
                  : 'choose_car_you_want_to_lease'.tr,
            )
          ],
        ),
      ),
    );
  }

  Widget _note(BuildContext context) {
    return Obx(
      () => controller.selectedOption == 'own'
          ? Container(
              padding: EdgeInsets.only(
                left: 16.0.w,
                right: 16.0.w,
                top: 8.0.h,
              ),
              decoration: BoxDecoration(
                color: context.containerColor,
              ),
              child: NotesWithBoldWidget(
                notes: [
                  isShowDocsUploader
                      ? 'clear_document_note'.tr
                      : 'note_car_model'.tr
                ],
              ),
            )
          : SizedBox.shrink(),
    );
  }

  bool get isShowDocsUploader =>
      controller.selectedOption == 'own' &&
      (controller.modelVehicleSelection.value?.shouldRentCar ?? true).inverted;

  bool get isShowRentButton =>
      controller.selectedOption == 'own' &&
      controller.modelVehicleSelection.value?.shouldRentCar == true;
  _button(BuildContext context) {
    return Obx(
      () => CustomButton(
        margin: EdgeInsetsDirectional.only(start: 16.w, end: 16.w),
        text: isShowRentButton ? 'i_want_to_rent_car'.tr : 'next'.tr,
        colorText: Colors.white,
        onPressed: isShowRentButton
            ? controller.selectWantRent
            : controller.saveStepUberFlow,
        enabled: isShowRentButton
            ? true
            : (controller.selectedOption == 'rent' &&
                    controller.vehicleItemSelection != null &&
                    controller.pricingVehiclesItemSelection != null) ||
                controller.selectedOption == 'own' &&
                    controller.selectedDocument != null,
        colorButton: context.appPrimaryColor,
      ),
    );
  }

  _divider(BuildContext context) {
    return Divider(
        height: 1.sp,
        thickness: 1.sp,
        color: context.black.withValues(alpha: 0.07));
  }

  void _onClickBack() {
    if (controller.arguments?.fromDashboard ?? false) {
      final args = InstructionUberFlowArguments(
        fromDashboard: true,
      );
      Get.offNamed(
        AppRoutes.instructionsUberFlowPage,
        arguments: args,
      );
    } else {
      Get.back();
    }
  }

  void _onPopInvoked(didPop, dynamic) async {
    if (controller.arguments?.fromDashboard ?? false) {
      final args = InstructionUberFlowArguments(
        fromDashboard: true,
      );
      Get.offNamed(
        AppRoutes.instructionsUberFlowPage,
        arguments: args,
      );
    } else {
      Get.back();
    }
  }
}
