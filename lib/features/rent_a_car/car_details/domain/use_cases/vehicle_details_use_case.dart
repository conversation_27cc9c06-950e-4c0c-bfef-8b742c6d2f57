import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/car_details_entity.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/repositories/i_vehicle_details_repository.dart';

class GetLeaseVehiclesDetailsUseCase
    implements UseCase<CarDetailsEntity?, int> {
  final VehicleDetailsRepository vehicleDetailsRepository;
  GetLeaseVehiclesDetailsUseCase(this.vehicleDetailsRepository);
  @override
  Future<Either<Failure, CarDetailsEntity?>> call(int param) {
    return vehicleDetailsRepository.getVehicleDetails(param);
  }
}
