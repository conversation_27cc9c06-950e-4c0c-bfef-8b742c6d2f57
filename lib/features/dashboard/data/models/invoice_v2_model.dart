import '../../../invoices/domain/entities/invoice_v2.dart';

class InvoiceV2Model extends InvoiceV2 {
  final DateTime? creationDate;
  @override
  final String? title;
  final String? subTitle;
  @override
  final bool? isPaid;
  final String? pdfUrl;

  const InvoiceV2Model({
    required super.id,
    required super.status,
    required super.amount,
    required this.creationDate,
    required this.pdfUrl,
    required this.title,
    required this.subTitle,
    required this.isPaid,
  }) : super(
          title: title,
          description: subTitle,
          url: pdfUrl,
          downloadUrl: pdfUrl,
          isPaid: isPaid,
        );

  factory InvoiceV2Model.fromJson(Map<String, dynamic> json) {
    return InvoiceV2Model(
      id: json['id'],
      pdfUrl: json['pdf_url'],
      status: json['status'],
      amount: json['amount'],
      creationDate:
          json['creation'] != null ? DateTime.parse(json['creation']) : null,
      title: json['title'],
      subTitle: json['sub_title'],
      isPaid: json['is_paid'],
    );
  }
}
