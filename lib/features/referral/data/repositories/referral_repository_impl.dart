// lib/data/repositories/under_processing_repository_impl.dart
import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/failures.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/referral_status.dart';
import '../../domain/entities/referral_status_values.dart';
import '../../domain/repositories/referral_repository.dart';
import '../data_sources/referral_remote_data_source.dart';

class ReferralRepositoryImpl implements ReferralRepository {
  final ReferralRemoteDataSource remoteDataSource;
  final NetworkInfo? networkInfo;

  ReferralRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, ReferralStatus?>> getReferralStatus() async {
    if (await networkInfo?.isConnected == true) {
      try {
        return Right(await remoteDataSource.getReferralStatus());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, ReferralStatusValues?>>
      getReferralStatusValues() async {
    if (await networkInfo?.isConnected == true) {
      try {
        return Right(await remoteDataSource.getReferralStatusValues());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
