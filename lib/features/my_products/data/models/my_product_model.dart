import 'package:thrivve/features/my_products/domain/entities/my_product.dart';

class MyProductModel extends MyProduct {
  const MyProductModel({
    required String? status,
    required int? contractId,
    required String? contractPrice,
    required List<ContractDetail>? contractDetails,
    required String? contractPeriodType,
    required String? contractType,
    required String? startDate,
    required String? endDate,
    required String? extraNote,
    required int? id,
    required String? priceNote,
    required double? rate,
    required String? monthlyPriceTitle,
    required List<Feature>? featuresEn,
    required List<Feature>? featuresAr,
    required String? title,
    required String? manufactureYear,
    required int? vehicleId,
    required String? period,
    required bool? fixedFleet,
    required String? currency,
    required String? monthlyPrice,
    required String? mainGalleryImage,
    required String? mainImage,
    required String? dailyPriceBetween,
    required List<String>? notes,
  }) : super(
          status: status,
          contractId: contractId,
          contractPrice: contractPrice,
          contractDetails: contractDetails,
          contractPeriodType: contractPeriodType,
          contractType: contractType,
          startDate: startDate,
          endDate: endDate,
          extraNote: extraNote,
          id: id,
          priceNote: priceNote,
          rate: rate,
          monthlyPriceTitle: monthlyPriceTitle,
          featuresEn: featuresEn,
          featuresAr: featuresAr,
          title: title,
          manufactureYear: manufactureYear,
          vehicleId: vehicleId,
          period: period,
          fixedFleet: fixedFleet,
          currency: currency,
          monthlyPrice: monthlyPrice,
          mainGalleryImage: mainGalleryImage,
          mainImage: mainImage,
          dailyPriceBetween: dailyPriceBetween,
          notes: notes,
        );

  factory MyProductModel.fromJson(Map<String, dynamic> json) {
    final vehicle = json['vehicle'] as Map<String, dynamic>?;

    return MyProductModel(
      status: json['status'],
      contractId: json['contract_id'],
      contractPrice: json['contract_price'],
      contractDetails: (json['contract_details'] as List<dynamic>?)
          ?.map((detail) => ContractDetail(
                description: detail['description'] ?? '',
                value: detail['value'] ?? '',
              ))
          .toList(),
      contractPeriodType: json['contract_period_type'],
      contractType: json['contract_type'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      extraNote: json['extra_note'],
      id: json['id'],
      // Vehicle details
      priceNote: vehicle?['price_note'],
      rate: vehicle?['rate']?.toDouble(),
      monthlyPriceTitle: vehicle?['monthly_price_title'],
      featuresEn: (vehicle?['features_en'] as List<dynamic>?)
          ?.map((feature) => Feature(
                title: feature['title'] ?? '',
                key: feature['key'] ?? '',
                icon: feature['icon'] ?? '',
                value: feature['value'] ?? '',
              ))
          .toList(),
      featuresAr: (vehicle?['features_ar'] as List<dynamic>?)
          ?.map((feature) => Feature(
                title: feature['title'] ?? '',
                key: feature['key'] ?? '',
                icon: feature['icon'] ?? '',
                value: feature['value'] ?? '',
              ))
          .toList(),
      title: vehicle?['title'],
      manufactureYear: vehicle?['manufacture_year'],
      vehicleId: vehicle?['vehicle_id'],
      period: vehicle?['period'],
      fixedFleet: vehicle?['fixed_fleet'],
      currency: vehicle?['currency'],
      monthlyPrice: vehicle?['monthly_price']?.toString(),
      mainGalleryImage: vehicle?['main_gallery_image'],
      mainImage: vehicle?['main_image']?['url'],
      dailyPriceBetween: vehicle?['daily_price_between'],
      notes: (vehicle?['notes'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'contract_id': contractId,
      'contract_price': contractPrice,
      'contract_details': contractDetails
          ?.map((detail) => {
                'description': detail.description,
                'value': detail.value,
              })
          .toList(),
      'contract_period_type': contractPeriodType,
      'contract_type': contractType,
      'start_date': startDate,
      'end_date': endDate,
      'extra_note': extraNote,
      'id': id,
      'vehicle': {
        'price_note': priceNote,
        'rate': rate,
        'monthly_price_title': monthlyPriceTitle,
        'features_en': featuresEn
            ?.map((feature) => {
                  'title': feature.title,
                  'key': feature.key,
                  'icon': feature.icon,
                  'value': feature.value,
                })
            .toList(),
        'features_ar': featuresAr
            ?.map((feature) => {
                  'title': feature.title,
                  'key': feature.key,
                  'icon': feature.icon,
                  'value': feature.value,
                })
            .toList(),
        'title': title,
        'manufacture_year': manufactureYear,
        'vehicle_id': vehicleId,
        'period': period,
        'fixed_fleet': fixedFleet,
        'currency': currency,
        'monthly_price': monthlyPrice,
        'main_gallery_image': mainGalleryImage,
        'main_image': mainImage != null ? {'url': mainImage} : null,
        'contract_type': contractType,
        'daily_price_between': dailyPriceBetween,
        'notes': notes,
      },
    };
  }
}
