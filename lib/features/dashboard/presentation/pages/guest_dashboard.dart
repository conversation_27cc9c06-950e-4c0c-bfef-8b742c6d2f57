import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/util/const.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/dashboard/presentation/pages/widgets/new_products_widget.dart';
import 'package:thrivve/features/dashboard/presentation/pages/widgets/product_section.dart';
import 'package:thrivve/features/dashboard/presentation/widgets/list_of_products_horizontal_shimmer.dart';
import 'package:thrivve/features/onboarding/presentation/widgets/login_signup/rent_car_sheet_login_register_sheet.dart';

class GuestDashboard extends StatefulWidget {
  @override
  State<GuestDashboard> createState() => _GuestDashboardState();
}

class _GuestDashboardState extends State<GuestDashboard>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    // Add observer for system theme changes
    WidgetsBinding.instance.addObserver(this);
    checkSystemTheming();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  bool? isDarkMode;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkSystemTheming();
    }
  }

  checkSystemTheming() {
    bool newDarkMode =
        MediaQuery.of(Get.context!).platformBrightness == Brightness.dark;
    log("newDarkMode is $newDarkMode");
    if (isDarkMode == null || isDarkMode != newDarkMode) {
      isDarkMode = newDarkMode;
      setState(() {});
    }
  }

  File? fileInfo;
  bool imageLoadingFromCache = false;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Stack(
        children: [
          if (getProductsRemoteConfig() == 2)
            Container(
              height: Get.height,
              width: Get.width,
              color: (isDarkMode == true) ? Colors.black : null,
              child: (isDarkMode == true)
                  ? null
                  : Image.asset(
                      "assets/thrivvePhotos/icons/background.jpg",
                      fit: BoxFit.fill,
                      alignment: Alignment.topCenter,
                    ),
            ),
          SafeArea(
            bottom: false,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Column(
                children: [
                  SizedBox(
                    height: 10.sp,
                  ),
                  notLogedInAppbar(),
                  Expanded(
                    child: CustomScrollView(
                      physics: ClampingScrollPhysics(),
                      slivers: [
                        SliverAppBar(
                          pinned: true,
                          floating: true,
                          backgroundColor: Colors.transparent,
                          surfaceTintColor: Colors.transparent,
                          expandedHeight: 85.sp,
                          flexibleSpace: FlexibleSpaceBar(
                            background: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 20.sp),
                                Row(
                                  children: [
                                    CustomTextWidget(
                                      title: "hello_guest".tr,
                                      size: 22,
                                      color: context.black,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    CustomTextWidget(
                                      title: " 👋",
                                      size: 22,
                                    ),
                                  ],
                                ).marginSymmetric(horizontal: 16.sp),
                                CustomTextWidget(
                                  title: "what_do_you_want_today_guest".tr,
                                  size: 13,
                                  color: context.black.withOpacity(0.6),
                                  fontWeight: FontWeight.w600,
                                ).marginOnly(left: 16.sp, right: 16.sp),
                                SizedBox(
                                  height: 5.sp,
                                ),
                              ],
                            ),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: Container(
                            color: Colors.transparent,
                            child: _buildStaticProductsWidget(),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: SizedBox(height: 10.sp),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildStaticProductsWidget() {
    return BlocBuilder<DashboardBloc, DashboardState>(
        buildWhen: (previous, current) =>
            previous.getNewProductsStatus != current.getNewProductsStatus,
        builder: (context, state) {
          if (state.getNewProductsStatus == AppStatus.loading) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(
                  height: 20.sp,
                ),
                (getProductsRemoteConfig() == 1
                    ? ListOfProductsHorizontalShimmer()
                    : Shimmer.fromColors(
                        baseColor: context.borderAddBranch,
                        highlightColor: context.appBackgroundColor,
                        child: NewProductsWidget()))
              ],
            );
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(
                height: 20.sp,
              ),
              (getProductsRemoteConfig() == 1
                  ? ProductsSection(
                      state: state,
                      personalInfo: null,
                      isInstantPaymentIsEnable: false,
                      showAllProducts: true,
                      showTitle: false,
                      isLogin: false,
                    )
                  : NewProductsWidget()),
            ],
          );
        });
  }

  Widget notLogedInAppbar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            child: SvgPicture.asset(
              Get.locale?.languageCode == valueArLanguage
                  ? "assets/thrivvePhotos/logo_ar.svg"
                  : "assets/thrivvePhotos/thrivve_header.svg",
              color: Get.find<ThemeController>().isDarkMode()
                  ? Colors.white
                  : null,
              fit: BoxFit.cover,
              width: 115.sp,
            ),
          ),
          const Spacer(),
          Row(
            children: [
              InkWell(
                onTap: () async {
                  showChangeLanguage(context);
                },
                child: SizedBox(
                  width: 24.sp,
                  height: 24.sp,
                  child: Image.asset(
                    "assets/thrivvePhotos/icons/language_logo.png",
                    color: context.outlineButtonColor,
                  ),
                ),
              ),
              SizedBox(
                width: 24.sp,
              ),
              InkWell(
                onTap: () {
                  showRentCarLoginOrRegisterSheet(
                    Get.context!,
                    dlParams: {},
                  );
                },
                child: SizedBox(
                  width: 24.sp,
                  height: 24.sp,
                  child: Image.asset(
                    "assets/thrivvePhotos/icons/login_logo.png",
                    color: context.outlineButtonColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
