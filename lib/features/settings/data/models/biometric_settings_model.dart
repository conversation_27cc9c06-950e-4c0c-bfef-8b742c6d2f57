import '../../domain/entities/biometric_settings.dart';

class BiometricSettingsModel extends BiometricSettings {
  BiometricSettingsModel({
    String? biometricType,
    bool? isBiometricEnabled,
  }) : super(
          biometricType: biometricType,
          isBiometricEnabled: isBiometricEnabled,
        );

  factory BiometricSettingsModel.fromJson(Map<String, dynamic> json) {
    return BiometricSettingsModel(
      biometricType: json['biometric_type'] as String?,
      isBiometricEnabled: json['is_biometric_enabled'] == true || json['is_biometric_enabled'] == 'true',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'biometric_type': biometricType,
      'is_biometric_enabled': isBiometricEnabled,
    };
  }
} 