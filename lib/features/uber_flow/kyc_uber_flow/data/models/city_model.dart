List<CityModel> listOfCityModelFromJson(List json) =>
    json.map((e) => CityModel.fromJson(e)).toList();

class CityModel {
  String? id;
  String? name;
  String? cityAlias;
  String? nameEn;
  String? nameAr;

  CityModel({this.id, this.name, this.cityAlias, this.nameEn, this.nameAr});
  CityModel copyWith(
      {String? id,
      String? name,
      String? cityAlias,
      String? nameEn,
      String? nameAr}) {
    return CityModel(
        id: id ?? this.id,
        name: name ?? this.name,
        cityAlias: cityAlias ?? this.cityAlias,
        nameAr: nameAr ?? this.nameAr,
        nameEn: nameEn ?? this.nameEn);
  }

  CityModel.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    name = json['name'];
    cityAlias = json['city_alias'];
    nameEn = json['name_en'];
    nameAr = json['name_ar'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['city_alias'] = this.cityAlias;
    data['name_en'] = this.nameEn;
    data['name_ar'] = this.nameAr;
    return data;
  }

  @override
  toString() {
    // TODO: implement ==
    return name ?? 'unknown';
  }
}
