import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/data_sources/i_link_uber_with_thrivve_data_source.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/mappers/link_uber_with_thrivve_response_extension.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/entities/link_uber_with_thrive_response_entity.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/i_link_uber_with_thrivve_repository.dart';

class LinkUberWithThrivveRepositoryImplentation
    extends LinkUberWithThrivveRepository {
  final LinkUberWithThrivveDataSource linkUberWithThrivveDataSource;
  final NetworkInfo? networkInfo;
  LinkUberWithThrivveRepositoryImplentation(
      this.linkUberWithThrivveDataSource, this.networkInfo);

  @override
  Future<Either<Failure, LinkUberWithThrivveResponseEntity?>>
      linkUberWithThrivve(LinkUberWithThrivveRequestParamsModel params) async {
    if (await networkInfo?.isConnected == true) {
      try {
        final result =
            await linkUberWithThrivveDataSource.linkUberWithThrivve(params);
        return Right((result?.toEntity()));
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
