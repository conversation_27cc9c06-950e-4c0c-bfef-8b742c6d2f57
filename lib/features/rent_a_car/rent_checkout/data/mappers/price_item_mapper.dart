import '../../domain/entities/price_item_entity.dart';
import '../models/checkout_response_model.dart';

extension PriceItemMapper on PriceItemModel {
  PriceItemEntity toEntity() {
    return PriceItemEntity(
      price: price,
      currency: currency,
      description: description,
      discountPrice: discountPrice,
      totalPrice: totalPrice,
    );
  }
}

extension PriceItemEntityMapper on PriceItemEntity {
  PriceItemModel toModel() {
    return PriceItemModel(
      price: price,
      currency: currency,
      description: description,
      discountPrice: discountPrice,
      totalPrice: totalPrice,
    );
  }
} 