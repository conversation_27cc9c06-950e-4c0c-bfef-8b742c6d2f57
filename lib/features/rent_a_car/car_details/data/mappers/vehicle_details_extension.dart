import 'package:thrivve/features/rent_a_car/car_details/data/models/vehicle_details_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/domain/entities/vehicle_details_entity.dart';

extension InfoItemExtension on InfoItemModel {
  InfoItemEntity toEntity() {
    return InfoItemEntity(
      icon: icon,
      description: description,
      value: value,
    );
  }
}

extension InfoExtension on InfoModel {
  InfoEntity toEntity() {
    return InfoEntity(
      title: title,
      items: items?.map((e) => e.toEntity()).toList(),
    );
  }
}
