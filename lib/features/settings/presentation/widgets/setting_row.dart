import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class SettingRow extends StatelessWidget {
  final String? icon;
  final String? title;
  final bool showIcon;
  final bool showArrow;
  final bool missedInfoExist;
  final Color? titleTextColor;
  final Function()? onTab;

  const SettingRow({
    super.key,
    this.icon,
    required this.title,
    required this.onTab,
    this.showArrow = true,
    this.showIcon = true,
    this.titleTextColor,
    this.missedInfoExist = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTab,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Visibility(
                visible: showIcon,
                child: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: context.appBackgroundColor,
                      borderRadius: BorderRadius.circular(90.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(8.0.w),
                      child: Center(
                          child: Image.asset(
                        icon ?? "",
                        width: 24.w,
                        height: 24.h,
                      )),
                    )),
              ),
              SizedBox(
                width: 8.w,
              ),
              Center(
                child: Text(
                  title ?? "",
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: titleTextColor ?? context.textTitle,
                  ),
                ),
              )
            ],
          ),
          Row(
            children: [
              Visibility(
                visible: missedInfoExist,
                child: Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.redAccent,
                  size: 12.sp,
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
              Visibility(
                visible: showArrow,
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 14.sp,
                  color: context.statusBackground,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
