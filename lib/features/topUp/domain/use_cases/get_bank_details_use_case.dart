import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/thrivve_bank_details.dart';
import '../repositories/top_up_repository.dart';

class GetThrivveBankDetailsDataUseCase
    extends UseCase<ThrivveBankDetails?, NoParams> {
  final TopUpRepository repository;

  GetThrivveBankDetailsDataUseCase({required this.repository});

  @override
  Future<Either<Failure, ThrivveBankDetails?>> call(NoParams params) async {
    return repository.getThrivveBankDetails();
  }
}
