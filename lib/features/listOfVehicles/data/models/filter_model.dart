import '../../domain/entities/filter.dart';

class FilterModel extends Filter {
  @override
  final int? id;
  @override
  final String? title;
  @override
  final String? icon;
  final String? type;
  @override
  final bool isSelected;


  const FilterModel({
    required this.id,
    required this.title,
    required this.icon,
    required this.type,
    this.isSelected = false,
  }) : super(
          id: id,
          title: title,
          icon: icon,
          isSelected: isSelected,
        );

  factory FilterModel.fromJson(Map<String, dynamic> json) {
    return FilterModel(
      type: json['type'],
      id: json['id'],
      title: json['title'],
      icon: json['icon'],
    );
  }
}
