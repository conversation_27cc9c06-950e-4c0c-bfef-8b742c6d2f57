import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // Import flutter_screenutil
import 'package:thrivve/core/extentions/optional_mapper.dart';

class CustomIconButton extends StatelessWidget {
  final String text;
  final IconData? icon;
  final String? assetsIcon;
  final Function() onTab;
  Color? fillColor;
  Color? textColor;
  double height;
  double? radius;
  bool? isReverse;
  TextStyle? textStyle;

  CustomIconButton(
      {super.key, // Fix the super.key
      required this.text,
      required this.onTab,
      this.icon,
      this.textStyle,
      this.radius,
      this.isReverse,
      this.assetsIcon,
      this.height = 30,
      this.fillColor,
      this.textColor}); // Fix the super.key

  @override
  Widget build(BuildContext context) {
    var rowWidgetList = [
      assetsIcon != null
          ? Image.asset(
              assetsIcon!,
              height: 24.h, // Use ScreenUtil to set height
              width: 24.w, // Use ScreenUtil to set width
            )
          : icon != null
              ? Icon(
                  icon,
                  color: textColor ?? Colors.white,
                  size: 14.sp, // Use ScreenUtil to set size
                )
              : Container(),
      SizedBox(width: 4.w), // Use ScreenUtil to set width
      Text(
        text,
        style: textStyle ??
            TextStyle(
              color: textColor ?? Colors.white,
              fontSize: 12.sp, // Use ScreenUtil to set font size
              fontWeight: FontWeight.w400,
            ),
      ),
    ];

    return Material(
      child: InkWell(
        onTap: onTab,
        child: Container(
          height: height.h,
          // Use ScreenUtil to set height
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          // Use ScreenUtil to set padding
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius ?? 20.r),
            // Use ScreenUtil to set border radius
            color: fillColor ?? context.appPrimaryColor,
            border: Border.all(
              color: context.appPrimaryColor!,
              width: 1.w,
            ), // Use ScreenUtil to set border width
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: isReverse == true
                ? rowWidgetList.reversed.toList()
                : rowWidgetList,
          ),
        ),
      ),
    );
  }
}
