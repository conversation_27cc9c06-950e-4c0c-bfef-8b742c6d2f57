import 'package:flutter/material.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class ListTileSettingsWidget extends StatelessWidget {
  final Function()? tap;
  final String? title;
  final String? supTitle;
  final Widget? widget;
  final String? imageIcon;
  final int? status;
  final bool warring;

  const ListTileSettingsWidget(
      {super.key,
      this.tap,
      this.title,
      this.supTitle,
      this.widget,
      this.imageIcon,
      this.status,
      this.warring = false});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: tap,
      title: Text(
        title!,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: warring ? Colors.red : context.black,
            fontWeight: FontWeight.w500),
      ),
      subtitle: supTitle != null
          ? Text(
              supTitle!,
              style: TextStyle(
                  fontFamily: 'cairo',
                  fontSize: 12,
                  color: context.statusBackground,
                  fontWeight: FontWeight.w400),
            )
          : null,
      leading: imageIcon != null
          ? Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: context.iconBackgroundColor,
                border: Border.all(width: 1, color: context.iconBoarderColor!),
                borderRadius: BorderRadius.circular(90),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                    child: Image.asset(
                  imageIcon!,
                  width: 24,
                  height: 24,
                )),
              ))
          : null,
      trailing: widget,
    );
  }
}
