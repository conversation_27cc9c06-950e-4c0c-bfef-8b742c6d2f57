import 'dart:async';
import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/notifications_fcm/firebase_messaging_impl.dart';
import 'package:thrivve/core/quick_actions/app_quick_actions.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/util/static_var.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/change_pin_use_case.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/verify_pin_use_case.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/injection_container.dart';
import '../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/util/helper.dart';
import '../../../dashboard/domain/use_cases/get_support_url_use_case.dart';
import '../../../onboarding/domain/entities/verify_otp.dart';
import '../../../onboarding/domain/use_cases/send_otp_use_case.dart';
import '../../../onboarding/domain/use_cases/verify_otp_use_case.dart';
import '../../../pin/domain/entities/thrivve_user.dart';
import '../../domain/use_cases/sing_in_use_case.dart';

part 'pin_event.dart';
part 'pin_state.dart';

class PinBloc extends Bloc<PinEvent, PinState> {
  final VerifyPinUseCase verifyPinUseCase;
  final ChangePinUseCase changePinUseCase;
  final SignInUseCase signInUseCase;
  final SendOtpUseCase sendOtpUseCase;
  final VerifyOtpUseCase verifyOtpUseCase;
  final LocalAuthentication auth = LocalAuthentication();
  final UserSecureDataSource? userSecureDataSource;
  final GetSupportUrlUseCase getSupportUrlUseCase;

  PinBloc({
    required this.changePinUseCase,
    required this.verifyPinUseCase,
    required this.signInUseCase,
    required this.sendOtpUseCase,
    required this.userSecureDataSource,
    required this.verifyOtpUseCase,
    required this.getSupportUrlUseCase,
  }) : super(const PinState()) {
    on<GetSingInEvent>(_getSingInEventOnClick);
    on<CheckPinCode>(_checkEnterPinCode);
    on<ChangePinCode>(_changePinCode);
    on<BackToNewPin>(_backToNewPinCode);
    on<AddNewPinCode>(_addNewPinCode);
    on<AddNumToPinEvent>(_addNumToPinEventOnClick);
    on<RemovePinEvent>(_removePinEventOnClick);
    on<NavigateToMainScreenEvent>(_navigateToMainScreenEventOnClick);
    on<SendOtpEvent>(_getSendOTPEventOnClick);
    on<VerifyOtpEvent>(_getVerifyOtpEventOnClick);
    on<BackToFirstTempPasscodeEvent>(_backToFirstTempPasscodeEventOnClick);
    on<ResentVerificationCodeEvent>(_getReSendOTPEventOnClick);
    on<GetSupportUrlEvent>(_getSupportUrlEventOnClick);
    on<SaveLeadIdEvent>(_saveLeadIdEventOnClick);
    on<SetupNewPinCodeEvent>(_setupNewPin);
    on<CheckIsDeviceSupportAndTypeBioMetricEvent>(_checkBiometricSupport);
    on<VerifyBiometricEvent>(_verifyBiometric);
    on<GetUserNameEvent>(_getUserNameEventOnClick);
  }
  Future<void> _getUserNameEventOnClick(
    GetUserNameEvent event,
    Emitter<PinState> emit,
  ) async {
    var thrivveUser = await userSecureDataSource?.getUserData();
    emit(state.copyWith(userName: () => thrivveUser?.fullName ?? ''));
  }

  Future<void> _verifyBiometric(
    VerifyBiometricEvent event,
    Emitter<PinState> emit,
  ) async {
    try {
      final bool isAuthenticated = await auth.authenticate(
        localizedReason: 'please_auth_to_continue'.tr,
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
      if (isAuthenticated) {
        // If biometric authentication is successful, proceed with pin verification
        emit(state.copyWith(
          biometricStatus: () => AppStatus.success,
        ));
      }
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> _checkBiometricSupport(
    CheckIsDeviceSupportAndTypeBioMetricEvent event,
    Emitter<PinState> emit,
  ) async {
    try {
      final bool canCheckBiometrics = await auth.canCheckBiometrics;
      final bool isDeviceSupported = await auth.isDeviceSupported();
      final bool isBioMetricEnabled =
          await userSecureDataSource?.isBioMetricEnabled() ?? false;

      // Get available biometrics
      final List<BiometricType> availableBiometrics =
          await auth.getAvailableBiometrics();

      // Check if device has Face ID or Touch ID
      final bool isFaceEnabled =
          availableBiometrics.contains(BiometricType.face);

      if (isDeviceSupported && isBioMetricEnabled) {
        add(const VerifyBiometricEvent());
      }
      emit(state.copyWith(
        isBiometricSupported: () => canCheckBiometrics && isDeviceSupported,
        isBioMetricEnabled: () => isBioMetricEnabled,
        isFaceEnabled: () => isFaceEnabled,
      ));
    } catch (e) {
      emit(state.copyWith(
        isBiometricSupported: () => false,
        isBioMetricEnabled: () => false,
        isFaceEnabled: () => false,
      ));
    }
  }

  Future<void> _setupNewPin(
    SetupNewPinCodeEvent event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(changePinCodeStep: () => 1));
  }

  Future<void> _saveLeadIdEventOnClick(
    SaveLeadIdEvent event,
    Emitter<PinState> emit,
  ) async {
    if (event.leadId != null) {
      userSecureDataSource?.setLeadId(event.leadId);
      getIt
          .get<IAnalyticsLogger>()
          .logProperty(AnalyticsActions.leadId, event.leadId!);
    }
  }

  Future<void> _getSupportUrlEventOnClick(
    GetSupportUrlEvent event,
    Emitter<PinState> emit,
  ) async {
    if (state.supportUrl != null && state.supportUrl?.isNotEmpty == true) {
      openWhatsApp(url: state.supportUrl ?? "");
      return;
    }
    final getSupportUrlUseCaseData = await getSupportUrlUseCase(NoParams());
    getSupportUrlUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      emit(state.copyWith(supportUrl: () => data));
      openWhatsApp(url: data ?? "");
    });
  }

  Future<void> _backToFirstTempPasscodeEventOnClick(
    BackToFirstTempPasscodeEvent event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
      firstPin: () => "",
      pinNum: () => "",
      secondTempPasscode: () => false,
      secondTempNotMatch: () => false,
    ));
  }

  Future<void> _getReSendOTPEventOnClick(
    ResentVerificationCodeEvent event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
        reSendOtpStatus: () => AppStatus.loading, counterReStart: () => false));

    final sendOtpUseCaseData = await sendOtpUseCase(
      SendOptParams(
        isLogin: event.isLogin,
        mobile: event.mobileNum,
      ),
    );
    sendOtpUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            reSendOtpStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) {
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => data?.otp,
        counterReStart: () => true,
        reSendOtpStatus: () => AppStatus.success,
      ));
    });
  }

  Future<void> _getVerifyOtpEventOnClick(
    VerifyOtpEvent event,
    Emitter<PinState> emit,
  ) async {
    if (event.otpNum == null || event.otpNum?.isEmpty == true) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_empty_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    } else if (event.otpNum?.length != 6) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    }

    emit(state.copyWith(verifyStatus: () => AppStatus.loading));

    final verifyOtpUseCaseData = await verifyOtpUseCase(VerifyOptParams(
      mobile: event.mobileNum,
      otpCode: event.otpNum,
    ));
    verifyOtpUseCaseData?.fold(
      (failure) => emit(
        state.copyWith(
          verifyStatus: () => AppStatus.failure,
          errorMessage: () => mapFailureToMessage(failure),
        ),
      ),
      (data) async {
        saveLoginTokenLocalStorage(data,
            userSecureDataSource: userSecureDataSource);
        emit(state.copyWith(
          verifyOtp: () => data,
          verifyStatus: () => AppStatus.success,
        ));
      },
    );
  }

  Future<void> saveLoginTokenLocalStorage(VerifyOtp? data,
      {required UserSecureDataSource? userSecureDataSource}) async {
    userSecureDataSource?.setTempToken(data?.token ?? "");
  }

  Future<void> _getSendOTPEventOnClick(
    SendOtpEvent event,
    Emitter<PinState> emit,
  ) async {
    var mobile = await userSecureDataSource?.getMobileNumber();
    if (mobile == null) return;
    emit(state.copyWith(
      sendOtpStatus: () => AppStatus.loading,
      mobileNum: () => mobile,
    ));

    final sendOtpUseCaseData = await sendOtpUseCase(
      SendOptParams(
        isLogin: true,
        mobile: mobile,
      ),
    );
    sendOtpUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                  sendOtpStatus: () => AppStatus.failure,
                  errorMessage: () => mapFailureToMessage(failure)),
            ), (data) {
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => data?.otp,
        sendOtpStatus: () => AppStatus.success,
      ));
    });
  }

  Future<void> _navigateToMainScreenEventOnClick(
    NavigateToMainScreenEvent event,
    Emitter<PinState> emit,
  ) async {
    if (event.from != null) {
      Get.back(result: event.from == "lifecycle");
      Future.delayed(Duration(seconds: 1)).then((x) {
        AppQuickActions().checkAndExecutePendingAction();
      });
    } else {
      final thrivveUser = await getIt<UserSecureDataSource>().getUserData();
      if (StaticVar.reviewAndCheckoutParams != null &&
          thrivveUser?.applicationTypeEnum == null) {
        reviewRentApplication(StaticVar.reviewAndCheckoutParams!);
      } else {
        Get.offAllNamed(AppRoutes.homePage,
            parameters: {isPinSetupSuccessfully: false.toString()});
        Future.delayed(Duration(seconds: 2)).then((x) {
          AppQuickActions().checkAndExecutePendingAction();
        });
      }
    }
  }

  Future<void> _removePinEventOnClick(
    RemovePinEvent event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
        pinNum: () => state.pinNum.isNotEmpty
            ? state.pinNum.substring(0, state.pinNum.length - 1)
            : state.pinNum));
  }

  Future<void> _addNumToPinEventOnClick(
    AddNumToPinEvent event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
      pinNum: () => "${state.pinNum}${event.num}",
      secondTempNotMatch: () => false,
    ));
  }

  Future<void> _checkEnterPinCode(
    CheckPinCode event,
    Emitter<PinState> emit,
  ) async {
    if (event.pinCode == null || event.pinCode!.isEmpty) {
      emit(state.copyWith(
        status: () => AppStatus.failure,
        pinNum: () => "",
        errorMessage: () => "please_enter_pin".tr,
      ));
      return;
    }

    emit(state.copyWith(
      status: () => AppStatus.loading,
      attemptNumber: () => state.attemptNumber + 1,
    ));
    final verifyPinUseData =
        await verifyPinUseCase(VerifyPinParams(pinCode: event.pinCode));
    await verifyPinUseData?.fold((failure) async {
      if (state.attemptNumber == 3) {
        Get.back();
        emit(state.copyWith(
          status: () => AppStatus.failure,
          pinNum: () => "",
          firstPin: () => "",
          secondTempPasscode: () => false,
          errorMessage: () => "many_errors_attempts".tr,
        ));
      } else {
        if (failure is ServerFailure) {
          _emitFailureState(failure.msj ?? '', emit, state);
        } else if (failure is NetworkFailure) {
          _emitFailureState(failure.msj ?? '', emit, state);
        } else {
          _emitFailureState('unknown_error'.tr, emit, state);
        }
      }
    }, (data) async {
      emit(
        state.copyWith(
          pinNum: () => '',
          changePinCodeStep: () => 1,
          oldPinNum: () => event.pinCode ?? '',
          status: () => AppStatus.success,
        ),
      );
    });
  }

  Future<void> _addNewPinCode(
    AddNewPinCode event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
        changePinCodeStep: () => 2,
        attemptNumber: () => 0,
        newPinNum: () => event.pinCode!,
        pinNum: () => ""));
  }

  Future<void> _backToNewPinCode(
    BackToNewPin event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
      firstPin: () => "",
      pinNum: () => "",
      newPinNum: () => "",
      attemptNumber: () => 0,
      changePinCodeStep: () => 1,
    ));
  }

  Future<void> _changePinCode(
    ChangePinCode event,
    Emitter<PinState> emit,
  ) async {
    emit(state.copyWith(
      status: () => AppStatus.loading,
      attemptNumber: () => state.attemptNumber + 1,
    ));
    log("attemptNumber ${state.attemptNumber}");
    if (state.newPinNum == event.pinCode!) {
      ChangePinParams input;
      if (event.isFromChange == false) {
        input = ChangePinParams(
          pinCode: event.pinCode,
        );
      } else {
        input = ChangePinParams(
            pinCode: state.oldPinNum, newPinCode: event.pinCode);
      }
      final changePinUseCaseUseCaseData = await changePinUseCase(
        input,
      );
      await changePinUseCaseUseCaseData?.fold((failure) async {
        if (state.attemptNumber == 3) {
          Get.back();
          emit(state.copyWith(
            status: () => AppStatus.failure,
            pinNum: () => "",
            firstPin: () => "",
            secondTempPasscode: () => false,
            errorMessage: () => "many_errors_attempts".tr,
          ));
        } else {
          String message = '';
          if (failure is ServerFailure) {
            message = failure.msj ?? '';
            // Handle server-specific logic
          } else if (failure is NetworkFailure) {
            message = failure.msj ?? '';
            // Handle network-specific logic
          } else {
            message = 'Unknown error occurred';
          }
          _emitFailureState(message, emit, state);
        }
      }, (data) async {
        emit(state.copyWith(
          successChangePinMessage: () => data?.message,
          successChangePinTitle: () => data?.title,
          pinNum: () => "",
          changePinCodeStep: () => 3,
          newPinNum: () => event.pinCode!,
          status: () => AppStatus.success,
        ));
      });
    } else {
      if (state.attemptNumber == 3) {
        emit(state.copyWith(
          secondsDuration: () => 2,
          status: () => AppStatus.failure,
          pinNum: () => "",
          errorMessage: () => "many_errors_attempts".tr,
        ));
      } else {
        emit(state.copyWith(
          secondsDuration: () => 2,
          status: () => AppStatus.failure,
          errorMessage: () => "not_matched_pins".tr,
          pinNum: () => "",
        ));
      }
    }
  }

  Future<void> _getSingInEventOnClick(
    GetSingInEvent event,
    Emitter<PinState> emit,
  ) async {
    await login(
      emit,
      event,
      userSecureDataSource: userSecureDataSource,
    );
  }

// Utility function to log errors
  void logError(String message, StackTrace stacktrace) {
    // Implement logging logic here (e.g., send to a logging service)
    print("$message\n$stacktrace");
  }

  Future<void> login(
    Emitter<PinState> emit,
    GetSingInEvent event, {
    required UserSecureDataSource? userSecureDataSource,
  }) async {
    String platformVersion = await getPlatFormVersion();
    String? language = Get.locale?.languageCode; //todo
    String platform = getPlatForm();
    var versionPlatform = await PackageInfo.fromPlatform();
    var appVersion = versionPlatform.version;

    final fcmToken =
        await FireBaseMessagingImpl.fireBaseMessagingImpl.getTokenFirebase();
    print("pushNotificationToken $fcmToken");

    emit(
      state.copyWith(
        attemptNumber: () => state.attemptNumber + 1,
        loginStatus: () => AppStatus.loading,
      ),
    );
    final signInUseCaseData = await signInUseCase(
      SignInParams(
        pin: state.pinNum,
        isNew: state.isRequiredPinSetup,
        platform: platform,
        platformVersion: platformVersion,
        appVersion: appVersion,
        pushNotificationToken: fcmToken,
        language: language,
      ),
    );

    await signInUseCaseData?.fold((failure) async {
      emit(
        state.copyWith(
          loginStatus: () => AppStatus.failure,
        ),
      );
      await handleFailure(failure, state, userSecureDataSource, emit);
    }, (data) async {
      var leadId = await userSecureDataSource?.getLeadId();
      addPinAnalyticsEvent(leadId ?? "");
      await saveUserToLocalStorage(
        data,
        userSecureDataSource,
      );
      emit(
        state.copyWith(
          thrivveUser: () => data,
          loginStatus: () => AppStatus.success,
        ),
      );
    });
  }
}

void addPinAnalyticsEvent(String userId) {
  getIt.get<IAnalyticsLogger>().logEvent(AnalyticsActions.pinEntered,
      parameters: {AnalyticsActions.userID: userId});
}

Future<void> handleFailure(Failure failure, PinState state,
    userSecureDataSource, Emitter<PinState> emit) async {
  final errorCode = mapFailureToErrorCode(failure);
  final errorMessage = mapFailureToMessage(failure);

  if (errorCode == 400) {
    if (state.attemptNumber >= 3) {
      _clearTokenAndRedirect(userSecureDataSource);
    } else {
      _emitFailureState(errorMessage, emit, state);
    }
  } else if (errorCode == 401) {
    errorSnackBar(context: Get.context!, message: errorMessage);
    _clearTokenAndRedirect(userSecureDataSource);
  } else {
    _emitFailureState(errorMessage, emit, state);
  }
}

void _clearTokenAndRedirect(UserSecureDataSource? userSecureDataSource) {
  userSecureDataSource?.clearAll();
  Get.offAllNamed(AppRoutes.homePage);
}

void _emitFailureState(
    String errorMessage, Emitter<PinState> emit, PinState state) {
  emit(state.copyWith(
    status: () => AppStatus.failure,
    pinNum: () => "",
    firstPin: () => "",
    secondTempPasscode: () => false,
    errorMessage: () => errorMessage,
  ));
}
