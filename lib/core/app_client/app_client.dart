import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';

enum RequestType {
  post,
  get,
}

class ApiResponse<T> {
  final T? data;
  final int statusCode;
  final String? message;

  ApiResponse({
    required this.data,
    required this.statusCode,
    this.message,
  });
}

class ApiClient {
  final Dio dio;
  final UserSecureDataSource userSecureDataSource;

  ApiClient({
    required this.dio,
    required this.userSecureDataSource,
  });

  // Helper method to remove null values from maps
  Map<String, dynamic> _removeNullValues(Map<String, dynamic> map) {
    return Map.fromEntries(
      map.entries.where((entry) => entry.value != null),
    );
  }

  // Helper method to recursively clean maps and lists
  dynamic _cleanValue(dynamic value) {
    if (value is Map<String, dynamic>) {
      return _removeNullValues(value);
    } else if (value is List) {
      return value.map((item) => _cleanValue(item)).toList();
    }
    return value;
  }

  Future<Map<String, dynamic>> _getHeaders(
      {Map<String, dynamic>? additionalHeaders,
      bool removeNulls = false}) async {
    final defaultHeaders = <String, dynamic>{
      ApiSettings.countryCodeHeader:
          await userSecureDataSource.getCountryCode(),
      ApiSettings.languageHeader: findLanguage(),
      ApiSettings.deviceFingerprintHeader:
          await userSecureDataSource.getDeviceFingerprint()
    };

    if (additionalHeaders != null) {
      defaultHeaders.addAll(additionalHeaders);
    }

    return removeNulls ? _removeNullValues(defaultHeaders) : defaultHeaders;
  }

  Future<ApiResponse<T>> request<T>({
    required String endpoint,
    required T Function(dynamic) fromJson,
    RequestType method = RequestType.get,
    Object? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    bool removeNullValues = true,
  }) async {
    try {
      final finalHeaders = await _getHeaders(
          additionalHeaders: headers, removeNulls: removeNullValues);

      // Clean data if it's a map
      Object? cleanedData = data;
      if (removeNullValues && data is Map<String, dynamic>) {
        cleanedData = _cleanValue(data);
      }

      // Clean query parameters
      Map<String, dynamic>? cleanedQueryParameters = queryParameters;
      if (removeNullValues && queryParameters != null) {
        cleanedQueryParameters = _removeNullValues(queryParameters);
      }

      Response response;
      if (method == RequestType.get) {
        response = await dio.get(
          endpoint,
          queryParameters: cleanedQueryParameters,
          options: Options(
            headers: finalHeaders,
            validateStatus: (status) => true, // Accept all status codes
          ),
        );
      } else if (method == RequestType.post) {
        response = await dio.post(
          endpoint,
          data: cleanedData,
          queryParameters: cleanedQueryParameters,
          options: Options(
            headers: finalHeaders,
            validateStatus: (status) => true, // Accept all status codes
          ),
          onSendProgress: onSendProgress,
          cancelToken: cancelToken,
        );
      } else {
        throw ServerException(
          msj: 'Unsupported HTTP method: $method',
          code: 500,
        );
      }
      // Handle response based on status code
      final statusCode = response.statusCode ?? 500;

      if (statusCode == 200) {
        // Success - parse and return data
        log("✅ 200 Success: $endpoint");
        final T result = fromJson(response.data);
        return ApiResponse(
          data: result,
          statusCode: statusCode,
          message: response.statusMessage,
        );
      } else if (statusCode == 500) {
        // 500 Server Error - Send to Sentry and Firebase Crashes
        final errorContext = {
          "endpoint": endpoint,
          "method": method.toString(),
          "status_code": statusCode,
          "response_message": response.statusMessage,
          "response_data": response.data,
          "response_headers": response.headers.map,
          "request_data": cleanedData,
          "query_parameters": cleanedQueryParameters,
          "timestamp": DateTime.now().toIso8601String(),
        };

        // Send to Sentry
        SentryService.instance.captureMessage(
          "500 Server Error: $endpoint",
          level: SentryLevel.error,
          data: errorContext,
        );

        // Send to Firebase Crashes
        FirebaseCrashlytics.instance.recordError(
          ServerException(
            msj: response.statusMessage ?? "Internal Server Error",
            code: statusCode,
          ),
          StackTrace.current,
          reason: "500 Server Error in API response",
          information: [errorContext.toString()],
        );

        // Add breadcrumb
        SentryService.instance.addBreadcrumb(
          message: "500 Server Error: $endpoint",
          category: "api_error",
          data: {
            "endpoint": endpoint,
            "method": method.toString(),
            "status_code": statusCode,
          },
        );

        // Throw exception with real status code
        throw ServerException(
          msj: response.statusMessage ?? "Internal Server Error",
          code: statusCode,
        );
      } else {
        // Other non-200 status codes - throw exception with real status code
        log("❌ Error ${statusCode}: $endpoint - ${response.statusMessage}");
        throw ServerException(
          msj: response.statusMessage ?? "Request failed",
          code: statusCode,
        );
      }
    } on DioException catch (ex, stackTrace) {
      final statusCode = ex.response?.statusCode ?? 500;
      final errorMessage =
          getErrorFromErrorList(ex.response?.data) ?? ex.message;

      // Prepare error context
      final errorContext = {
        "endpoint": endpoint,
        "method": method.toString(),
        "request_data": data,
        "query_parameters": queryParameters,
        "headers": headers,
        "response_status_code": statusCode,
        "response_data": ex.response?.data,
        "error_message": errorMessage,
        "error_type": ex.type.toString(),
        "timestamp": DateTime.now().toIso8601String(),
      };

      log("❌ DioException [$method] $endpoint: Status $statusCode - $errorMessage");

      if (statusCode == 500) {
        // 500 Server Error - Enhanced tracking
        SentryService.instance.captureException(
          ex,
          stackTrace: stackTrace,
          data: {
            "error_type": "dio_server_error_500",
            "endpoint": endpoint,
            "method": method.toString(),
            "status_code": statusCode,
            "server_response": ex.response?.data,
            "response_headers": ex.response?.headers.map,
            "request_context": errorContext,
            "timestamp": DateTime.now().toIso8601String(),
          },
        );

        // Send to Firebase Crashes
        FirebaseCrashlytics.instance.recordError(
          ex,
          stackTrace,
          reason: "DioException 500 Server Error",
          information: [errorContext.toString()],
        );

        // Add breadcrumb
        SentryService.instance.addBreadcrumb(
          message: "DioException 500 Error: $endpoint",
          category: "api_error",
          data: {
            "endpoint": endpoint,
            "method": method.toString(),
            "status_code": statusCode,
            "error_message": errorMessage,
          },
        );
      }

      // Throw exception with real status code (for all DioExceptions)
      throw ServerException(
        msj: errorMessage,
        code: statusCode,
      );
    } catch (ex, stackTrace) {
      // Handle unexpected errors (network issues, parsing errors, etc.)
      final errorContext = {
        "endpoint": endpoint,
        "method": method.toString(),
        "request_data": data,
        "query_parameters": queryParameters,
        "headers": headers,
        "error_message": ex.toString(),
        "error_type": "unexpected_error",
        "timestamp": DateTime.now().toIso8601String(),
      };

      log("❌ Unexpected Error [$method] $endpoint: ${ex.toString()}");

      // Send to Sentry (all unexpected errors)
      SentryService.instance.captureException(
        ex,
        stackTrace: stackTrace,
        data: {
          "error_type": "unexpected_api_error",
          "endpoint": endpoint,
          "method": method.toString(),
          "status_code": 500, // Default to 500 for unexpected errors
          "request_context": errorContext,
          "timestamp": DateTime.now().toIso8601String(),
        },
      );

      // Send to Firebase Crashes (all unexpected errors)
      FirebaseCrashlytics.instance.recordError(
        ex,
        stackTrace,
        reason: "Unexpected error in ApiClient",
        information: [errorContext.toString()],
      );

      // Throw exception with 500 status code for unexpected errors
      throw ServerException(
        msj: ex.toString(),
        code: 500,
      );
    }
  }
}
