import 'package:dartz/dartz.dart';
import 'package:thrivve/features/notifications/domain/entities/transaction_status_entity.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/notifications_list.dart';
import '../../domain/entities/unseen_number.dart';
import '../../domain/repositories/notification_repository.dart';
import '../data_sources/notification_remote_data_source.dart';

class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationRemoteDataSource remoteDataSource;

  final NetworkInfo networkInfo;

  NotificationRepositoryImpl(
      {required this.remoteDataSource, required this.networkInfo});

  @override
  Future<Either<Failure, NotificationList?>>? getListOfNotifications(
      {int? page, int? perPage}) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getListOfNotifications(
            page: page, perPage: perPage));
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, UnseenNumber?>>? getUnseenNotificationNum() async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getUnseenNotificationNum());
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, bool?>>? setSeenToAllNotification(
      {required List<int>? notificationIds, bool? isActionTaken}) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.setSeenToAllNotification(
            notificationIds: notificationIds, isActionTaken: isActionTaken));
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, TransactionStatusEntity?>>? getTranactionStatus(
      String id) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getTranactionStatus(id: id));
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }
}
