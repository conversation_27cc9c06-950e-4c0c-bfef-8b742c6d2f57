import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:thrivve/core/api/api_settings.dart'; // Contains API constants
import 'package:thrivve/core/app_routes.dart'; // Route constants
import 'package:thrivve/core/error/exceptions.dart'; // Custom exceptions
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/model/refresh_token_model.dart'; // Refresh token model
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/withdraw/presentation/manager/withdraw_bloc.dart';
import 'package:thrivve/generated/assets.dart'; // Asset constants

class AuthInterceptor extends Interceptor {
  final Dio dio;
  final UserSecureDataSource userSecureDataSource;

  AuthInterceptor(this.dio, this.userSecureDataSource);
  bool isRefreshing = false;
  List<Function> requestQueue = [];

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final accessToken = await userSecureDataSource.getAccessToken() ??
        await userSecureDataSource.getTempToken();

    final deviceFingerprint = await userSecureDataSource.getDeviceFingerprint();
    final countryCode = await userSecureDataSource.getCountryCode();
    final language = findLanguage();

    if (accessToken != null) {
      options.headers[ApiSettings.authorizationHeader] = accessToken;
    }

    if (deviceFingerprint != null) {
      options.headers[ApiSettings.deviceFingerprintHeader] = deviceFingerprint;
    }

    options.headers[ApiSettings.countryCodeHeader] = countryCode;
    options.headers[ApiSettings.languageHeader] = language;

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final statusCode = err.response?.statusCode;
    final requestOptions = err.requestOptions;

    //  // Handle only 409 (token expired)
    //     if (statusCode == 419 && requestOptions.extra["retried"] != true) {
    //       requestOptions.extra["retried"] = true;
    if (statusCode == 419) {
      if (!isRefreshing) {
        isRefreshing = true;
        try {
          final refreshToken = await userSecureDataSource.getRefreshToken();
          if (refreshToken == null) {
            throw UnauthorisedException('No refresh token available');
          }

          final response = await dio.post(
            ApiSettings.refreshTokenEndpoint,
            data: {"refresh_token": refreshToken},
          );

          if (response.statusCode == 200 && response.data != null) {
            final refreshModel = RefreshTokenModel.fromJson(response.data);
            final newAccessToken = refreshModel.accessToken;
            final newRefreshToken = refreshModel.refreshToken;

            if (newAccessToken != null && newRefreshToken != null) {
              // Save new tokens
              await userSecureDataSource.saveTokens(
                  newAccessToken, newRefreshToken);

              isRefreshing = false;
              // Retry all queued requests
              final queueCopy = List<Function>.from(requestQueue);
              requestQueue.clear();

              for (var cb in queueCopy) {
                try {
                  await cb();
                } catch (e) {
                  log('Error retrying queued request: $e');
                  // If a queued request fails with 419, it will be handled by the interceptor
                }
              }

              // Retry this request
              final response = await dio.fetch(requestOptions);
              return handler.resolve(response);
            } else {
              throw ServerException(
                msj: 'Invalid token response format',
                code: response.statusCode,
              );
            }
          } else {
            throw ServerException(
              msj: 'Token refresh failed',
              code: response.statusCode,
            );
          }
        } catch (e) {
          log(e.toString());
          isRefreshing = false;
          requestQueue.clear();
          await userSecureDataSource.clearAll();
          _goToLogin(err);
          return;
        }
      } else {
        // Wait for refresh and retry after
        final response = await _queueRequest(() => dio.fetch(requestOptions));
        return handler.resolve(response);
      }
    } else if (err.response?.statusCode == 401 ||
        (err.response?.statusCode == 400 &&
            (err.response?.data['message'] ==
                    'Not Logged Token, please complete login process' ||
                err.response?.data['message'] ==
                    'Device fingerprint is required for token version 2 or higher'))) {
      await userSecureDataSource.clearAll();
      _goToLogin(err);
      return;
    } else if (err.response?.statusCode == 409) {
      _goToSettingPage(err);
      return;
    }

    return handler.next(err);
  }

  Future<Response> _queueRequest(Future<Response> Function() requestFn) {
    final completer = Completer<Response>();
    requestQueue.add(() async {
      try {
        final res = await requestFn();
        completer.complete(res);
      } catch (e) {
        completer.completeError(e);
      }
    });
    return completer.future;
  }

  void _goToLogin(DioException err) {
    reinitializeBloc();
    getx.Get.offAllNamed(AppRoutes.homePage);
  }

  void _goToSettingPage(DioException err) async {
    final isLogin = await userSecureDataSource.isLogin();
    if (isLogin) {
      final route = getx.Get.currentRoute;
      if (route != AppRoutes.splash) {
        final errorMessage =
            err.response?.data?['message'] ?? 'An error occurred';
        showGeneralBottomSheet(
            context: getx.Get.context!,
            description: errorMessage,
            icon: Assets.assetsThrivvePhotosIconPasscode,
            iconColor: getx.Get.context!.black,
            title: "action_required".tr,
            isSvg: true,
            firstBtnText: 'set_pin_description'.tr,
            firstBtnOnClick: () async {
              getx.Get.back();
              await getx.Get.toNamed(
                Routes.changePinScreen,
              );
              if (route == AppRoutes.withdrawPage) {
                getIt<WithdrawBloc>().add(ValidateWithDrawEvent());
              }
            },
            secondBtnText: "back_button".tr,
            secondBtnOnClick: () {
              getx.Get.back();
            });
      }
    }
  }
}
