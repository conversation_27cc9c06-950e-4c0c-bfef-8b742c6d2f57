import 'package:flutter_test/flutter_test.dart';
import 'package:thrivve/features/bankAccount/domain/entities/add_bank.dart';
import 'package:thrivve/features/bankAccount/domain/use_cases/update_bank_account_use_case.dart';
import 'package:dartz/dartz.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'add_bank_account_use_case_test.dart';

void main() {
  late MockBankRepository mockBankRepository;
  late UpdateBankAccountUseCase updateBankAccountUseCase;
  setUp(() {
    mockBankRepository = MockBankRepository();
    updateBankAccountUseCase =
        UpdateBankAccountUseCase(bankRepository: mockBankRepository);
  });

  var paymentType = "paymentType";
  var bankId = 1;
  var paymentMethodId = 1;
  var beneficiaryIban = "beneficiaryIban";
  var beneficiaryName = "beneficiaryName";
  var addBank = const AddBankSuccessfully(
    message: "Bank Added Successfully",
  );

  test("test update bank account use case", () async {
    // arrange
    when(mockBankRepository.updateBankDetails(
            paymentType: paymentType,
            bankId: bankId,
            beneficiaryIban: beneficiaryIban,
            beneficiaryName: beneficiaryName,
            paymentMethodId: paymentMethodId))
        .thenAnswer((_) async => Right(addBank));

    // act

    var result = await updateBankAccountUseCase(UpdateBankParams(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));

    // assert
    verify(mockBankRepository.updateBankDetails(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));
    expect(result, Right(addBank));
  });

  test("test update bank account use case when return NetworkFailure",
      () async {
    // arrange
    when(mockBankRepository.updateBankDetails(
            paymentType: paymentType,
            bankId: bankId,
            beneficiaryIban: beneficiaryIban,
            beneficiaryName: beneficiaryName,
            paymentMethodId: paymentMethodId))
        .thenAnswer((_) async => Left(NetworkFailure()));

    // act

    var result = await updateBankAccountUseCase(UpdateBankParams(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));

    // assert
    verify(mockBankRepository.updateBankDetails(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));
    expect(result, Left(NetworkFailure()));
  });

  test("test update bank account use case when return ServerFailure", () async {
    // arrange
    when(mockBankRepository.updateBankDetails(
            paymentType: paymentType,
            bankId: bankId,
            beneficiaryIban: beneficiaryIban,
            beneficiaryName: beneficiaryName,
            paymentMethodId: paymentMethodId))
        .thenAnswer((_) async => Left(ServerFailure()));

    // act

    var result = await updateBankAccountUseCase(UpdateBankParams(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));

    // assert
    verify(mockBankRepository.updateBankDetails(
        paymentType: paymentType,
        bankId: bankId,
        beneficiaryIban: beneficiaryIban,
        beneficiaryName: beneficiaryName,
        paymentMethodId: paymentMethodId));
    expect(result, Left(ServerFailure()));
  });
}
