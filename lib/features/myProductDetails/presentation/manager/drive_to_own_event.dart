part of 'drive_to_own_bloc.dart';

@immutable
abstract class DriveToOwnEvent extends Equatable {
  const DriveToOwnEvent();

  @override
  List<Object?> get props => [];
}

class GetDriveToOwnDataEvent extends DriveToOwnEvent {
  final String? contractId;

  const GetDriveToOwnDataEvent({this.contractId});

  @override
  List<Object?> get props => [contractId];
}


class SetCurrentContractIdEvent extends DriveToOwnEvent {
  final String? contractId;

  const SetCurrentContractIdEvent({this.contractId});

  @override
  List<Object?> get props => [contractId];
}

class GetDriveToOwnDataInstallmentsEvent extends DriveToOwnEvent {
  const GetDriveToOwnDataInstallmentsEvent();

  @override
  List<Object?> get props => [];
}

class GetDriveToOwnDataAttachmentEvent extends DriveToOwnEvent {
  const GetDriveToOwnDataAttachmentEvent();

  @override
  List<Object?> get props => [];
}

class ReportIncidentEvent extends DriveToOwnEvent {
  final String? incidentType;
  final String? incidentDate;
  final String? incidentTime;
  final String? incidentLocation;
  final String? incidentDescription;
  final String? incidentFrontImage;
  final String? incidentBackImage;
  final String? incidentSideImage;
  final bool? didYouCallNajm;

  const ReportIncidentEvent({
    this.incidentType,
    this.incidentDate,
    this.incidentTime,
    this.incidentLocation,
    this.incidentDescription,
    this.incidentFrontImage,
    this.incidentBackImage,
    this.incidentSideImage,
    this.didYouCallNajm,
  });

  @override
  List<Object?> get props => [
        incidentType,
        incidentDate,
        incidentTime,
        incidentLocation,
        incidentDescription,
        incidentFrontImage,
        incidentBackImage,
        incidentSideImage,
        didYouCallNajm,
      ];
}
