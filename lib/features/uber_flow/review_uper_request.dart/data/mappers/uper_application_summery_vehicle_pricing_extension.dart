import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_pricing_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_application_summery_vehicle_pricing_entity.dart';

extension UperApplicationVehiclePricingExtension
    on UperApplicationSummeryVehiclePricingModel {
  UperApplicationSummeryVehiclePricingEntity toEntity() {
    return UperApplicationSummeryVehiclePricingEntity(
        id: id, subtitle: subtitle, title: title);
  }
}
