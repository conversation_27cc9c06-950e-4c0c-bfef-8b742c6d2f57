import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_request_summery_vehicel_entity.dart';

extension UperApplicationVehicleExtension
    on UperApplicationSummeryVehicleModel {
  UperApplicationSummeryVehicleEntity toEntity() {
    return UperApplicationSummeryVehicleEntity(
        carBrand: carBrand,
        carColors: carColors,
        carMotorPower: carMotorPower,
        carName: carName,
        dailyPriceBetween: dailyPriceBetween,
        deliveryDate: deliveryDate,
        fuelType: fuelType,
        gearType: gearType,
        id: id,
        images: images,
        isVerifiedByUber: isVerifiedByUber,
        mainImage: mainImage,
        manufacturer: manufacturer,
        manufacturerModel: manufacturerModel,
        motorSize: motorSize,
        notes: notes,
        productionYear: productionYear,
        rate: rate,
        subTitle: subTitle,
        title: title,
        totalPrice: totalPrice,
        vehicleId: vehicleId,
        vehicleType: vehicleType);
  }
}
