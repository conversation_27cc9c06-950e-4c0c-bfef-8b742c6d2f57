import '../../domain/entities/country.dart';

class CountryModel extends Country {
  CountryModel({
    this.id,
    this.nameEn,
    this.nameAr,
    this.name,
    this.dial,
    this.code,
  }) : super(nameEn: nameEn, nameAr: nameAr, dial: dial, code: code, id: id);

  @override
  final int? id;
  @override
  final String? nameEn;
  @override
  final String? nameAr;
  final String? name;
  @override
  final String? dial;
  @override
  final String? code;

  factory CountryModel.fromJson(Map<String, dynamic> json) => CountryModel(
        id: json["id"],
        name: json["name"],
        nameEn: json["name_en"],
        nameAr: json["name_ar"],
        dial: json["dial"],
        code: json["code"],
      );
}
