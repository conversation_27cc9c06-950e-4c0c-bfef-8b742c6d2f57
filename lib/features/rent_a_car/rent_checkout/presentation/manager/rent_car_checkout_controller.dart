import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/iterable_compact_map.dart';
import 'package:thrivve/core/util/payment_utils.dart';
import 'package:thrivve/features/payment/domain/entities/saved_card_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/checkout_paymob_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/kyc_input_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/calendar_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/checkout_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/checkout_paymob_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_input_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/step_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/step_rent_car_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/get_checkout_rent_a_car_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/get_pickup_calendar_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/submit_checkout_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/usecases/submit_kyc_usecase.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/widgets/checkout_step_tile.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_method_entity.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/features/topUp/domain/use_cases/checkout_payment_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/list_saved_cards_use_case.dart';
import 'package:thrivve/features/topUp/domain/use_cases/payment_methods_use_case.dart';

class RentCarCheckoutController extends GetxController {
  RentCarCheckoutController({
    required this.paymentMethodsUseCase,
    required this.checkoutPaymentUseCase,
    required this.listSavedCardsUseCase,
    required this.getPickupCalendarUseCase,
    required this.getCheckoutRentACarUseCase,
    required this.submitKYCUseCase,
    required this.submitCheckoutUseCase,
    required this.iPageLoadingDialog,
  });

  final PaymentMethodsUseCase paymentMethodsUseCase;
  final CheckoutPaymentUseCase checkoutPaymentUseCase;
  final ListSavedCardsUseCase listSavedCardsUseCase;
  final GetPickupCalendarUseCase getPickupCalendarUseCase;
  final GetCheckoutRentACarUseCase getCheckoutRentACarUseCase;
  final SubmitKYCUseCase submitKYCUseCase;
  final SubmitCheckoutUseCase submitCheckoutUseCase;
  final IPageLoadingDialog iPageLoadingDialog;
  // Controls the expand/collapse state of the subscription overview
  RxBool isSubscriptionExpanded = false.obs;

  Rx<CheckoutStepStatus> personalDetailsStatus = CheckoutStepStatus.normal.obs;
  Rx<CheckoutStepStatus> pickUpDateStatus = CheckoutStepStatus.normal.obs;
  Rx<CheckoutStepStatus> paymentMethodStatus = CheckoutStepStatus.normal.obs;
  RxInt numberStep = 3.obs;
  RxBool isLoadingSavePersonalData = false.obs;
  RxBool isLoadingSaveConfirmDate = false.obs;

  Rxn<String> identityImage = Rxn<String>(null);
  Rxn<String> driverLicenseImage = Rxn<String>(null);
  Rxn<DateTime> dateTimePickUp = Rxn<DateTime>(null);
  Rx<List<PaymentMethodEntity>> paymentMethods =
      Rx<List<PaymentMethodEntity>>([]);
  Rx<List<SavedCardEntity>> savedCards = Rx<List<SavedCardEntity>>([]);
  Rxn<PaymentMethodEntity> paymentSelection = Rxn(null);
  Rxn<bool> isLoadingPayment = Rxn<bool>(false);
  Rxn<String> errorPayment = Rxn<String>(null);

  // Calendar related state
  Rx<String?> selectedDate = ''.obs;
  String? selectedRadioDateForApi = '';
  Rx<String?> selectedRadioDateValue = ''.obs;
  RxBool isLoadingCalendar = false.obs;
  RxString calendarError = ''.obs;
  Rx<CalendarResponseEntity?> calendarResponse =
      Rx<CalendarResponseEntity?>(null);

  // Checkout response
  final Rx<CheckoutResponseEntity?> _checkoutResponse =
      Rx<CheckoutResponseEntity?>(null);
  final RxBool _isLoadingCheckout = false.obs;
  final RxString _checkoutError = ''.obs;

  // KYC response
  final Rx<KYCResponseEntity?> _kycResponse = Rx<KYCResponseEntity?>(null);
  final RxBool _isLoadingKYC = false.obs;
  final RxString _kycError = ''.obs;

  // Checkout Paymob response
  final Rx<CheckoutResponsePaymobEntity?> _checkoutPaymobResponse =
      Rx<CheckoutResponsePaymobEntity?>(null);
  final RxBool _isLoadingCheckoutPaymob = false.obs;
  final RxString _checkoutPaymobError = ''.obs;

  // Sentry transaction tracking
  ISentrySpan? _currentTransaction;

  CheckoutResponseEntity? get checkoutResponse => _checkoutResponse.value;
  bool get isLoadingCheckout => _isLoadingCheckout.value;
  String get checkoutError => _checkoutError.value;

  KYCResponseEntity? get kycResponse => _kycResponse.value;
  bool get isLoadingKYC => _isLoadingKYC.value;
  String get kycError => _kycError.value;

  CheckoutResponsePaymobEntity? get checkoutPaymobResponse =>
      _checkoutPaymobResponse.value;
  bool get isLoadingCheckoutPaymob => _isLoadingCheckoutPaymob.value;
  String get checkoutPaymobError => _checkoutPaymobError.value;

  bool get isRadioType => (calendarResponse.value?.selectionType == 'radio');

  void selectDate(String? date) {
    selectedDate.value = date;
    validatePickUpDate(true);
  }

  void selectRadioDate(String? date, String? apiDate) {
    selectedRadioDateForApi = apiDate;
    selectedRadioDateValue.value = date;

    validatePickUpDate(true);
  }

  @override
  void onInit() {
    super.onInit();
    _startChildSpan('checkout_process', 'checkout.start');
    _fetchPaymentMethods();
    _fetchCalendar();
    fetchSavedCardsEvent();
    fetchCheckoutRentACar();
  }

  // Sentry Helper Functions
  void _addBreadcrumb(String message,
      {String category = 'checkout', Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: message,
      category: category,
      data: data,
    );
  }

  void _captureMessage(String message,
      {SentryLevel level = SentryLevel.info, Map<String, dynamic>? data}) {
    SentryService.instance.captureMessage(
      message,
      level: level,
      data: data,
    );
  }

  Future<void> _captureException(
    dynamic exception, {
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) async {
    await SentryService.instance.captureException(
      exception,
      stackTrace: stackTrace,
      data: data,
    );
  }

  ISentrySpan? _startChildSpan(String name, String operation) {
    SentryService.instance.startChildSpan(name, operation);
  }

  void minusStepReamining() {
    --numberStep.value;
  }

  void toggleSubscriptionExpanded() {
    isSubscriptionExpanded.value = !isSubscriptionExpanded.value;
  }

  // Example validation logic
  void validatePersonalDetails(bool isValid) {
    personalDetailsStatus.value =
        isValid ? CheckoutStepStatus.done : CheckoutStepStatus.error;
  }

  void validatePickUpDate(bool isValid) {
    pickUpDateStatus.value =
        isValid ? CheckoutStepStatus.done : CheckoutStepStatus.error;
  }

  void validatePaymentMethod(bool isValid) {
    paymentMethodStatus.value =
        isValid ? CheckoutStepStatus.done : CheckoutStepStatus.error;
  }

  bool get isAllValid =>
      personalDetailsStatus.value == CheckoutStepStatus.done &&
      pickUpDateStatus.value == CheckoutStepStatus.done &&
      paymentMethodStatus.value == CheckoutStepStatus.done;

  // Sentry Tracking Functions

  void _trackRequestFailure(String operation, String error,
      {Map<String, dynamic>? data}) {
    _captureMessage(
      '$operation failed: $error',
      level: SentryLevel.error,
      data: data,
    );
  }

  void _trackFileUpload(String fileType, String status,
      {Map<String, dynamic>? data}) {
    _addBreadcrumb(
      '$fileType upload $status',
      category: 'file_upload',
      data: data,
    );
  }

  void _trackFileDelete(String fileType, {Map<String, dynamic>? data}) {
    _addBreadcrumb(
      '$fileType deleted',
      category: 'file_delete',
      data: data,
    );
  }

  void _trackStepStatus(StepRentCarEnum step, String status,
      {Map<String, dynamic>? data}) {
    _addBreadcrumb(
      '${step.name} step $status',
      category: 'step_status',
      data: data,
    );
  }

  void deleteIdentityPersonal() {
    _trackFileDelete('identity_document');
    identityImage.value = null;
  }

  void setIdentityPersonal(String? value) {
    if (value != null) {
      identityImage.value = value;
    }
  }

  void deleteDriverLicense() {
    _trackFileDelete('driver_license');
    driverLicenseImage.value = null;
  }

  void setDriverLicense(String? value) {
    if (value != null) {
      _trackFileUpload('driver_license', 'started');
      driverLicenseImage.value = value;
      _trackFileUpload('driver_license', 'completed');
    }
  }

  Future<void> fetchSavedCardsEvent() async {
    final result = await listSavedCardsUseCase.call(NoParams());
    result?.fold(
      (failure) {
        final failureError = mapFailureToMessage(failure);
        errorPayment.value = failureError;
        _trackRequestFailure('fetch_saved_cards', failureError);
      },
      (list) {
        savedCards.value = list?.noneNullList() ?? [];
      },
    );
  }

  Future<void> _fetchPaymentMethods() async {
    isLoadingPayment.value = true;
    errorPayment.value = null;
    final result = await paymentMethodsUseCase.call(NoParams());
    isLoadingPayment.value = false;
    result?.fold(
      (failure) {
        final failureError = mapFailureToMessage(failure);
        errorPayment.value = failureError;
        _trackRequestFailure('fetch_payment_methods', failureError);
      },
      (list) {
        paymentMethods.value = list?.noneNullList() ?? [];
        paymentMethods.value.removeWhere((e) => e.type == CardTypeEnum.Bank);
      },
    );
  }

  Future<void> _fetchCalendar() async {
    try {
      isLoadingCalendar.value = true;
      calendarError.value = '';
      final result = await getPickupCalendarUseCase.call(NoParams());
      result.fold(
        (failure) {
          calendarError.value = mapFailureToMessage(failure);
          _trackRequestFailure('fetch_calendar', calendarError.value);
        },
        (data) {
          calendarResponse.value = data;
        },
      );
    } catch (e, stackTrace) {
      calendarError.value = e.toString();
      await _captureException(e, stackTrace: stackTrace);
    } finally {
      isLoadingCalendar.value = false;
    }
  }

  Future<void> fetchCheckoutRentACar() async {
    try {
      _isLoadingCheckout.value = true;
      _checkoutError.value = '';
      final result = await getCheckoutRentACarUseCase.call(NoParams());
      result?.fold(
        (failure) {
          _checkoutError.value = mapFailureToMessage(failure);
          _trackRequestFailure('fetch_checkout', _checkoutError.value);
        },
        (data) {
          _checkoutResponse.value = data;

          // Update step statuses
          _updateStepStatuses(data);
        },
      );
    } catch (e, stackTrace) {
      _checkoutError.value = e.toString();
      await _captureException(e, stackTrace: stackTrace);
    } finally {
      _isLoadingCheckout.value = false;
    }
  }

  void _updateStepStatuses(CheckoutResponseEntity? data) {
    final steps = data?.steps ?? [];

    final dateStep =
        steps.firstWhereOrNull((e) => e.step == StepRentCarEnum.pickup_date);
    if (dateStep != null) {
      pickUpDateStatus.value = dateStep.status;
      selectedDate.value = dateStep.data?.pickupDate;
      selectedRadioDateForApi = dateStep.data?.pickupDate;
    }

    final personStep = steps
        .firstWhereOrNull((e) => e.step == StepRentCarEnum.personal_details);
    if (personStep != null) {
      personalDetailsStatus.value = personStep.status;
      driverLicenseImage.value = personStep.data?.drivingLicenceUrl;
      identityImage.value = personStep.data?.identityDocumentUrl;
      _trackStepStatus(
          StepRentCarEnum.personal_details, personStep.status.name);
    }

    final walletStep =
        steps.firstWhereOrNull((e) => e.step == StepRentCarEnum.payment_method);
    if (walletStep != null) {
      paymentMethodStatus.value = walletStep.status;
      paymentSelection.value = paymentMethods.value
          .firstWhereOrNull((e) => e.id == walletStep.data?.paymentMethodId);
      _trackStepStatus(StepRentCarEnum.payment_method, walletStep.status.name);
    }
  }

  void doCheckout() {
    if (checkoutChecked() && (checkoutResponse?.allowCheckOut ?? false)) {
      submitCheckout();
    }
  }

  bool checkoutChecked() {
    bool isAllDone = true;
    if (personalDetailsStatus.value != CheckoutStepStatus.done) {
      isAllDone = false;
      personalDetailsStatus.value = CheckoutStepStatus.error;
    }
    if (pickUpDateStatus.value != CheckoutStepStatus.done) {
      isAllDone = false;
      pickUpDateStatus.value = CheckoutStepStatus.error;
    }
    if (paymentMethodStatus.value != CheckoutStepStatus.done) {
      isAllDone = false;
      paymentMethodStatus.value = CheckoutStepStatus.error;
    }

    return isAllDone;
  }

  Future<void> submitKYC(KYCInputEntity input, StepRentCarEnum step) async {
    _isLoadingKYC.value = true;
    _kycError.value = '';

    try {
      final result = await submitKYCUseCase(input);
      result.fold(
        (failure) {
          _kycError.value = mapFailureToMessage(failure);
          _captureMessage(
            '${step.name} submission failed: ${_kycError.value}',
            level: SentryLevel.error,
          );
        },
        (response) {
          _kycResponse.value = response;
          _addBreadcrumb('${step.name} submitted successfully');
          Get.back();
        },
      );
    } catch (e, stackTrace) {
      await _captureException(
        e,
        stackTrace: stackTrace,
        data: {
          '${step.name}_input': input.toModel().toJson(),
        },
      );
    } finally {
      _isLoadingKYC.value = false;
      _finishSpan('checkout_step_${step.name}');
    }
  }

  Future<void> submitCheckout() async {
    _isLoadingCheckoutPaymob.value = true;
    _checkoutPaymobError.value = '';

    try {
      final result = await submitCheckoutUseCase(NoParams());
      result.fold(
        (failure) {
          _checkoutPaymobError.value = mapFailureToMessage(failure);
          _captureMessage(
            'Checkout submission failed: ${_checkoutPaymobError.value}',
            level: SentryLevel.error,
          );
          errorSnackBar(
            context: Get.context!,
            title: "err".tr,
            message: _checkoutPaymobError.value,
          );
        },
        (response) {
          _checkoutPaymobResponse.value = response;
          _addBreadcrumb('Checkout submitted successfully');
          _payPaymob(response);
        },
      );
    } catch (e, stackTrace) {
      await _captureException(
        e,
        stackTrace: stackTrace,
      );
    } finally {
      _isLoadingCheckoutPaymob.value = false;
      _finishSpan('checkout_process');
    }
  }

  Future<void> _payPaymob(CheckoutResponsePaymobEntity? response) async {
    _addBreadcrumb('starting_paymob_payment');
    if (response?.paymob?.clientSecret == null ||
        response?.paymob?.publicKey == null) {
      _captureMessage(
        'Missing Paymob credentials',
        level: SentryLevel.error,
      );
      return;
    }

    try {
      await payWithMobGeneral(
        clientSecret: response?.paymob?.clientSecret ?? '',
        publicKey: response?.paymob?.publicKey ?? '',
        savedCards: savedCards.value,
        context: Get.context!,
        isApplePay: paymentSelection.value?.type == CardTypeEnum.apple_pay,
        onSuccess: () {
          _addBreadcrumb('Payment completed successfully');
          _finishRentCarJourney();
          Get.offAllNamed(Routes.rentSuccessfullyPage, arguments: {
            'title': response?.title ?? '',
            'description': response?.body ?? '',
          });
        },
        onFailed: (error) {
          _captureMessage(
            'Payment failed: $error',
            level: SentryLevel.error,
          );
          Get.snackbar("", error.toString());
        },
      );
    } catch (e, stackTrace) {
      await _captureException(
        e,
        stackTrace: stackTrace,
        data: {
          'paymob_response': response?.toModel().toJson(),
        },
      );
    }
  }

  void trackCheckoutAbandonment() {
    _captureMessage(
      'User abandoned checkout process',
      level: SentryLevel.warning,
    );
  }

  Future<void> onClickStep(StepEntity item) async {
    _startChildSpan('checkout_step_${item.step?.name}',
        'checkout.${item.step?.name}.start');
    switch (item.step) {
      case StepRentCarEnum.personal_details:
        await Get.toNamed(AppRoutes.rentPersonalDetails);
        fetchCheckoutRentACar();
        break;
      case StepRentCarEnum.pickup_date:
        await Get.toNamed(AppRoutes.rentCalendarPage);
        fetchCheckoutRentACar();
        break;
      case StepRentCarEnum.payment_method:
        await Get.toNamed(AppRoutes.rentPaymentPage);
        fetchCheckoutRentACar();
      default:
        () {};
        break;
    }
  }

  @override
  void onClose() {
    _currentTransaction?.finish();
    super.onClose();
  }

  void _finishRentCarJourney() {
    SentryService.instance.finishTransaction();
  }

  void _finishSpan(String s) {
    SentryService.instance.finishSpan(s);
  }
}
