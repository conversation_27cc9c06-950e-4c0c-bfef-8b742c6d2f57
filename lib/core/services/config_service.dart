import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class ConfigService {
  static Future<void> initialize(String environment) async {
    await dotenv.load(fileName: '.env.$environment');
  }

  static String get googleMapsApiKey => dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  static String get sentryDsn => dotenv.env['SENTRY_DSN'] ?? '';
  static double get sentrySampleRate {
    final sampleRateStr = dotenv.env['SENTRY_SAMPLE_RATE'];
    if (sampleRateStr == null || sampleRateStr.isEmpty) {
      return 0.25; // Default sample rate for production
    }
    return double.tryParse(sampleRateStr) ?? 0.25;
  }
  static String get payMobPublicKey => dotenv.env['PAYMOB_PUBLIC_KEY'] ?? '';
  static String get payMobSecretKey => dotenv.env['PAYMOB_SECRET_KEY'] ?? '';
}
