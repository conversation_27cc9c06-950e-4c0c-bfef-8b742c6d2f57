import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/settings/domain/use_cases/verify_otp_for_change_phone_use_case.dart';

import '../../../../../core/localDataSource/user_secure_data_source.dart';
import '../../../../../core/user_cases/user_case.dart';
import '../../../../../core/util/general_helper.dart';
import '../../../../onboarding/domain/entities/country.dart';
import '../../../../onboarding/domain/entities/verify_otp.dart';
import '../../../../onboarding/domain/use_cases/get_countries_use_case.dart';
import '../../../../onboarding/domain/use_cases/get_current_country_code_use_case.dart';
import '../../../domain/use_cases/send_otp_for_change_phone_use_case.dart';

part 'update_mobile_event.dart';
part 'update_mobile_state.dart';

class UpdateMobileBloc extends Bloc<UpdateMobileEvent, UpdateMobileState> {
  SendOtpForChangePhoneUseCase sendOtpForChangePhoneUseCase;
  VerifyOtpForChangePhoneUseCase verifyOtpForChangePhoneUseCase;
  GetCountriesUseCase getCountriesUseCase;
  GetCurrentCountryCodeUseCase getCurrentCountryCodeUseCase;
  UserSecureDataSource? userSecureDataSource;

  UpdateMobileBloc({
    required this.sendOtpForChangePhoneUseCase,
    required this.verifyOtpForChangePhoneUseCase,
    required this.getCountriesUseCase,
    required this.getCurrentCountryCodeUseCase,
    required this.userSecureDataSource,
  }) : super(UpdateMobileState()) {
    on<SendOtpEvent>(_getSendOTPEventOnClick);
    on<VerifyOtpEvent>(_getVerifyOtpEventOnClick);
    on<GetCountriesEvent>(_getCountriesEventOnClick);
    on<UpdateMobileReadyToSubmitEvent>(_updateMobileReadyToSubmitEventOnClick);
    on<ResentVerificationCodeEvent>(_getReSendOTPEventOnClick);
  }

  Future<void> _getReSendOTPEventOnClick(
    ResentVerificationCodeEvent event,
    Emitter<UpdateMobileState> emit,
  ) async {
    emit(state.copyWith(
      reSendOtpStatus: () => AppStatus.loading,
    ));

    final sendOtpUseCaseData = await sendOtpForChangePhoneUseCase(
      SendOptParams(
        mobile: event.mobileNum,
      ),
    );
    sendOtpUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            reSendOtpStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) {
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => kDebugMode ? data?.otp : "",
        reSendOtpStatus: () => AppStatus.success,
      ));
    });
  }

  Future<void> _updateMobileReadyToSubmitEventOnClick(
    UpdateMobileReadyToSubmitEvent event,
    Emitter<UpdateMobileState> emit,
  ) async {
    emit(state.copyWith(
      readyToSubmit: () => event.readyToSubmit,
      mobileNum: () => event.mobileNum,
    ));
  }

  Future<void> _getCountriesEventOnClick(
    GetCountriesEvent event,
    Emitter<UpdateMobileState> emit,
  ) async {
    emit(state.copyWith(getCountriesStatus: () => AppStatus.loading));

    final getCurrentCountryCodeUseCaseData =
        await getCurrentCountryCodeUseCase(NoParams());
    getCurrentCountryCodeUseCaseData?.fold(
        (failure) => emit(
            state.copyWith(errorMessage: () => mapFailureToMessage(failure))),
        (data) => emit(state.copyWith(
              currentCountryCode: () => data,
            )));

    final getCountriesUseCaseData = await getCountriesUseCase(NoParams());
    getCountriesUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            getCountriesStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))),
        (data) => emit(state.copyWith(
              listOfCountries: () => data,
              selectedCountry: () {
                var list = data?.where((element) =>
                    element.code?.toLowerCase() == state.currentCountryCode);
                if (list?.isEmpty == true) {
                  return data?.firstWhere(
                      (element) => element.code?.toLowerCase() == "sa");
                } else {
                  return list?.first;
                }
              },
              getCountriesStatus: () => AppStatus.success,
            )));
  }

  Future<void> _getVerifyOtpEventOnClick(
    VerifyOtpEvent event,
    Emitter<UpdateMobileState> emit,
  ) async {
    if (event.otpNum.isEmpty == true) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_empty_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    } else if (event.otpNum.length != 6) {
      emit(state.copyWith(
        otpValidationErrorMessage: () => "otp_msj".tr,
        isOtpNotValid: () => true,
      ));
      return;
    }

    emit(state.copyWith(verifyStatus: () => AppStatus.loading));

    final verifyOtpUseCaseData =
        await verifyOtpForChangePhoneUseCase(VerifyOptParams(
      mobile: event.mobileNum,
      otpCode: event.otpNum,
    ));
    verifyOtpUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            verifyStatus: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) async {
      // saveLoginTokenLocalStorage(data,
      //     userSecureDataSource: userSecureDataSource);
      emit(state.copyWith(
        verifyOtp: () => data,
        verifyStatus: () => AppStatus.success,
      ));
    });
  }

  Future<void> _getSendOTPEventOnClick(
    SendOtpEvent event,
    Emitter<UpdateMobileState> emit,
  ) async {
    print("SendOtpEvent event: ${event.mobileNum}");
    emit(state.copyWith(
      mobileNum: () => event.mobileNum,
      status: () => AppStatus.loading,
    ));

    final sendOtpUseCaseData = await sendOtpForChangePhoneUseCase(SendOptParams(
      mobile: event.mobileNum,
    ));
    sendOtpUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            status: () => AppStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) {
      emit(state.copyWith(
        successSendOtpMessage: () => data?.message,
        otpCode: () => data?.otp,
        status: () => AppStatus.success,
      ));
    });
  }
}
