import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import '../../../../core/widget/list_tile_settings_widget.dart';
import '../../../../generated/assets.dart';
import '../../domain/entities/car_document.dart';
import 'bottom_sheet_title_widget.dart';

class MaintenanceBottomSheet extends StatelessWidget {
  List<CarDocument>? listOfMaintenance;

  MaintenanceBottomSheet({super.key, this.listOfMaintenance});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 64),
      padding: const EdgeInsets.only(bottom: 32),
      decoration: BoxDecoration(
          color: context.bottomsheetColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetTitleWidget(
            title: "maintenance".tr,
          ),
          Flexible(
              fit: FlexFit.tight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: ListView.builder(
                  shrinkWrap: false,
                  itemCount: listOfMaintenance?.length ?? 0,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return ListTileSettingsWidget(
                      title: listOfMaintenance?[index].name,
                      supTitle: "${listOfMaintenance?[index].date} "
                          "- ${listOfMaintenance?[index].size}",
                      imageIcon: Assets.thrivvePhotosDocument2,
                      tap: () {},
                    );
                  },
                ),
              )),
        ],
      ),
    );
  }
}
