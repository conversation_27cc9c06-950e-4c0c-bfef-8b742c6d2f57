import 'package:get/get.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/data_sources/i_kyc_source_implementation.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/data_sources/kyc_data_source_implementation.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/repositories/i_kyc_repository.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/repositories/kyc_repository_implementation.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/use_cases/get_all_cities_use_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/use_cases/get_uper_application_use_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/use_cases/kyc_submit_use_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/manager/kyc_controller.dart';

class KYCBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<KYCDataSource>(() => KYCDataSourceImplementation(getIt()));
    Get.lazyPut<KYCRepository>(() => KYCRepositoryImplementation(
        networkInfo: getIt(), uploadDocumentsDataSource: Get.find()));
    Get.lazyPut<GetAllCitiesUseCase>(() => GetAllCitiesUseCase(Get.find()));
    Get.lazyPut<KYCSubmitUseCase>(() => KYCSubmitUseCase(Get.find()));
    Get.lazyPut<GetUberApplicationInformationUseCase>(
        () => GetUberApplicationInformationUseCase(Get.find()));
    Get.lazyPut<KYCController>(() => KYCController(
          Get.find(),
          Get.find(),
          Get.find(),
        ));
  }
}
