import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  final FirebaseRemoteConfig _remoteConfig = FirebaseRemoteConfig.instance;
  bool _initialized = false;

  // Private constructor
  RemoteConfigService._internal();

  // Singleton instance
  static RemoteConfigService get instance => _instance;

  // Initialize remote config
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Set config settings
      await _remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));

      // // Set default values
      // await _remoteConfig.setDefaults({
      //   'enable_sentry': kDebugMode ? false : true,
      // });

      // Fetch remote config values
      await _remoteConfig.fetchAndActivate();

      _initialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Remote Config: $e');
      // Fallback to defaults
    }
  }

  // Get boolean value from remote config
  bool getBool(String key, {bool defaultValue = false}) {
    if (!_initialized) {
      debugPrint('Remote Config not initialized, returning default value');
      return defaultValue;
    }

    try {
      return _remoteConfig.getBool(key);
    } catch (e) {
      debugPrint('Error getting bool value for $key: $e');
      return defaultValue;
    }
  }

  // Get string value from remote config
  String getString(String key, {String defaultValue = ''}) {
    if (!_initialized) {
      debugPrint('Remote Config not initialized, returning default value');
      return defaultValue;
    }

    try {
      return _remoteConfig.getString(key);
    } catch (e) {
      debugPrint('Error getting string value for $key: $e');
      return defaultValue;
    }
  }

  // Get int value from remote config
  int getInt(String key, {int defaultValue = 0}) {
    if (!_initialized) {
      debugPrint('Remote Config not initialized, returning default value');
      return defaultValue;
    }

    try {
      return _remoteConfig.getInt(key);
    } catch (e) {
      debugPrint('Error getting int value for $key: $e');
      return defaultValue;
    }
  }

  // Get double value from remote config
  double getDouble(String key, {double defaultValue = 0.0}) {
    if (!_initialized) {
      debugPrint('Remote Config not initialized, returning default value');
      return defaultValue;
    }

    try {
      return _remoteConfig.getDouble(key);
    } catch (e) {
      debugPrint('Error getting double value for $key: $e');
      return defaultValue;
    }
  }

  // Check if Sentry is enabled
  bool get isSentryEnabled => getBool('enable_sentry', defaultValue: false);
}
