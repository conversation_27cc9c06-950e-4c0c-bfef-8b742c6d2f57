import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';

import '../../../../core/util/helper.dart';

class DynamicTextWidget extends StatelessWidget {
  final String text;
  final TextStyle style;

  const DynamicTextWidget({super.key, required this.text, required this.style});

  @override
  Widget build(BuildContext context) {
    return Linkify(
      onOpen: (link) {
        launchURL(url: link.url);
      },
      text: text,
      style: style,
      linkStyle: const TextStyle(
          color: Colors.blue, decoration: TextDecoration.underline),
    );
  }
}
