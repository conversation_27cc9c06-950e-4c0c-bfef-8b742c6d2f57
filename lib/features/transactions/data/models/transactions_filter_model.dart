
import '../../domain/entities/transactions_filter.dart';

class TransactionsFilterModel extends TransactionsFilter {
  @override
  final String? title;
  @override
  final String? value;
  @override
  final String? icon;

  const TransactionsFilterModel({
    required this.title,
    required this.value,
    required this.icon,
  }) : super(
          title: title,
          value: value,
          icon: icon,
        );

  factory TransactionsFilterModel.fromJson(Map<String, dynamic> json) {
    return TransactionsFilterModel(
      title: json['title'],
      value: json['value'],
      icon: json['icon'],
    );
  }
}
