import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

class MyTextFieldDatePicker extends StatefulWidget {
  final ValueChanged<DateTime> onDateChanged;
  final TextEditingController? controller;
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;
  final DateFormat? dateFormat;
  final FocusNode? focusNode;
  final String labelText;
  final Icon suffixIcon;
  final Color hintColor;
  final Color fillColor;
  final Color errorColorBorder;
  final bool isBorder;
  final bool isYearPicker;
  final bool isTimePicker;
  final bool isClickable;

  MyTextFieldDatePicker({
    super.key,
    required this.labelText,
    required this.suffixIcon,
    this.focusNode,
    this.dateFormat,
    required this.lastDate,
    required this.firstDate,
    required this.initialDate,
    required this.onDateChanged,
    required this.hintColor,
    required this.fillColor,
    required this.errorColorBorder,
    required this.controller,
    required this.isClickable,
    this.isBorder = true,
    this.isYearPicker = false,
    this.isTimePicker = false,
  })  : assert(!initialDate.isBefore(firstDate),
            'initialDate must be on or after firstDate'),
        assert(!initialDate.isAfter(lastDate),
            'initialDate must be on or before lastDate'),
        assert(!firstDate.isAfter(lastDate),
            'lastDate must be on or after firstDate');

  @override
  _MyTextFieldDatePicker createState() => _MyTextFieldDatePicker();
}

class _MyTextFieldDatePicker extends State<MyTextFieldDatePicker> {
  late DateFormat _dateFormat;
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();

    if (widget.dateFormat != null) {
      _dateFormat = widget.dateFormat!;
    } else {
      _dateFormat = DateFormat.yMd();
    }

    _selectedDate = widget.initialDate;
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      style: TextStyle(color: context.black),
      focusNode: widget.focusNode,
      controller: widget.controller,
      decoration: InputDecoration(
        suffixIcon: widget.suffixIcon,
        hintStyle: TextStyle(
            color: widget.hintColor,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamily: 'pop'),
        contentPadding:
            const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        fillColor: widget.fillColor,
        filled: true,
        border: InputBorder.none,
        hintText: widget.labelText,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            width: 1,
            color:
                widget.isBorder ? Colors.transparent : context.borderAddBranch!,
          ),
        ),
        errorStyle: TextStyle(
            fontFamily: 'pop',
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: widget.errorColorBorder),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            width: 1,
            color: widget.errorColorBorder,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide(
            width: 1,
            color: widget.errorColorBorder,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide(
            width: 2,
            color: context.appPrimaryColor!,
          ),
        ),
      ),
      onTap: () => {
        if (widget.isClickable)
          {
            if (widget.isYearPicker)
              {
                {_selectYear(context)}
              }
            else if (widget.isTimePicker)
              {
                {_selectTime(context)}
              }
            else
              {
                if (Theme.of(context).platform == TargetPlatform.iOS)
                  {_showDatePicker()}
                else
                  {_selectDate(context)}
              }
          }
      },
      readOnly: true,
    );
  }

  @override
  void dispose() {
    widget.controller?.dispose();
    super.dispose();
  }

  Future<void> _selectYear(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            widget.labelText,
          ),
          content: SizedBox(
            width: 300,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(DateTime.now().year - 100, 1),
              lastDate: DateTime(DateTime.now().year + 100, 1),
              initialDate: DateTime.now(),
              selectedDate: _selectedDate,
              onChanged: (DateTime dateTime) {
                _selectedDate = dateTime;
                widget.controller?.text = dateTime.year.toString();
                widget.onDateChanged(_selectedDate);
                Navigator.pop(context);
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectTime(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            widget.labelText,
          ),
          content: SizedBox(
            width: 300,
            height: 300,
            child: TimePickerDialog(
              initialTime: TimeOfDay.now(),
              initialEntryMode: TimePickerEntryMode.input,
              onEntryModeChanged: (value) {
                if (kDebugMode) {
                  print(value);
                }
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
    );

    if (pickedDate != null && pickedDate != _selectedDate) {
      setState(() {
        _selectedDate = pickedDate;
        widget.controller?.text = _dateFormat.format(pickedDate);
        widget.onDateChanged(_selectedDate);
      });
    }

    if (widget.focusNode != null) {
      widget.focusNode!.nextFocus();
    }
  }

  void _showDatePicker() {
    showCupertinoModalPopup(
        context: context,
        builder: (BuildContext builder) {
          return Container(
            height: MediaQuery.of(context).copyWith().size.height * 0.25,
            color: context.containerColor,
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date,
              onDateTimeChanged: (value) {
                if (value != _selectedDate) {
                  setState(() {
                    _selectedDate = value;
                    widget.controller?.text = _dateFormat.format(_selectedDate);
                    widget.onDateChanged(_selectedDate);
                  });
                }
              },
              initialDateTime: DateTime.now(),
              minimumYear: DateTime.now().year,
              maximumYear: DateTime.now().year + 100,
            ),
          );
        });
  }
}
