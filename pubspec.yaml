name: thrivve
description: Thrivve is the ultimate financial solution for gig workers. Manage your money effortlessly, access earnings instantly, and explore tailored financial products. Join us now for financial empowerment!

publish_to: 'none'


version: 1.22.3+193



environment:
  sdk: '>=3.0.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  file_picker: ^8.1.7
  flutter_screenutil: ^5.9.3
  flutter_cached_pdfview: ^0.4.2
  sentry_flutter:
    git:
      url: https://github.com/WeDeliverGitHub/sentry-dart.git
      ref: patch8.10.0
      path: flutter
  path:
  path_provider: ^2.1.4
  table_calendar:
  syncfusion_flutter_gauges: ^27.1.51
  country_icons: ^3.0.0
  get: ^4.6.6
  dio: ^5.7.0
  flutter_slidable: ^4.0.0
  flutter_dotenv: ^5.2.1
  dio_intercept_to_curl: ^0.2.0
  dependency_validator:
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  webview_flutter: ^4.9.0
  intl: ^0.19.0
  dartz: ^0.10.1
  internet_connection_checker: ^3.0.1
  connectivity_plus: ^6.1.2
  flutter_secure_storage: ^9.2.2
  firebase_core: ^3.9.0
  firebase_remote_config: ^5.2.0
  firebase_messaging: ^15.1.6
  firebase_crashlytics: ^4.2.0
  firebase_analytics: ^11.3.6
  package_info_plus: ^8.0.2
  timer_count_down: ^2.2.2
  shimmer: ^3.0.0
  get_it: ^8.0.0
  fl_chart: ^0.69.0
  image_picker: ^1.1.2
  url_launcher: ^6.3.1
  local_auth: ^2.3.0
  device_info_plus: ^10.1.2
  open_settings_plus: ^0.3.3
  flutter_local_notifications: ^17.2.3
  share_plus: ^10.0.2
  http: ^1.2.2
  flutter_spinkit: ^5.2.1
  flutter_pagewise: ^2.0.4
  permission_handler: ^11.3.1
  video_player: ^2.9.2
  flutter_downloader: ^1.11.8
  cached_network_image: ^3.4.1
  app_settings: ^5.1.1
  open_file_plus: ^3.4.1+1
  smooth_page_indicator: ^1.2.0+3
  otp_autofill: ^4.0.0
  pin_code_fields: ^8.0.1
  expandable_page_view: ^1.0.17
  shared_preferences: ^2.5.1
  flutter_localizations:
    sdk: flutter
  flutter_linkify: ^6.0.0
  chewie: ^1.8.5
  photo_view: ^0.15.0
  flutter_branch_sdk: ^8.5.0
  flutter_svg: ^2.0.16
  flutter_inappwebview: ^6.1.5
  quick_actions: ^1.1.0
  dropdown_button2: ^2.3.9
  mime: ^2.0.0
  carousel_slider: ^5.0.0
  flutter_widget_from_html: ^0.16.0
  flutter_cache_manager: ^3.4.1
dev_dependencies:
  mockito: ^5.4.4
  mocktail: ^1.0.4
  bloc_test: ^9.1.7
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:

  uses-material-design: true

  fonts:
    - family: SaudiRiyalFont
      fonts:
        - asset: assets/fonts/SaudiRiyalFont.ttf

    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Black.ttf
          weight: 900
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/NotoSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/NotoSans-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/NotoSans-Italic.ttf
          weight: 400
        - asset: assets/fonts/NotoSans-Light.ttf
          weight: 300
        - asset: assets/fonts/NotoSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/NotoSans-Thin.ttf
          weight: 100

    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Black.ttf
          weight: 900
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
        - asset: assets/fonts/NotoSansArabic-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/NotoSansArabic-ExtraLight.ttf
          weight: 400
        - asset: assets/fonts/NotoSansArabic-Light.ttf
          weight: 300
        - asset: assets/fonts/NotoSansArabic-Medium.ttf
          weight: 500
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansArabic-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/NotoSansArabic-Thin.ttf
          weight: 100

 

  assets:
    - assets/thrivveVideos/
    - assets/thrivvePhotos/
    - assets/thrivvePhotos/icons/
    - assets/thrivvePhotos/payment_method/
    - assets/thrivvePhotos/rent_a_car/
    - assets/thrivvePhotos/uber_flow/
    - assets/fonts/
    - assets/sounds/
    - assets/
    - assets/thrivvePhotos/under_proccessing_icon.png
    - assets/thrivvePhotos/vuesax/
    - assets/thrivvePhotos/vuesax/linear/
    - assets/thrivvePhotos/uber_flow/icon_close.svg
    - .env.dev
    - .env.stg
    - .env.prod
    - assets/thrivvePhotos/uber_flow/icon_check.svg
    - assets/thrivvePhotos/finger_image.svg