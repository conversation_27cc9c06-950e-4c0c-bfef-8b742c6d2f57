import 'package:equatable/equatable.dart';

class PriceItemEntity extends Equatable {
  final double? price;
  final String? currency;
  final String? description;
  final double? discountPrice;
  final double? totalPrice;

  const PriceItemEntity({
    this.price,
    this.currency,
    this.description,
    this.discountPrice,
    this.totalPrice,
  });

  @override
  List<Object?> get props => [
        price,
        currency,
        description,
        discountPrice,
        totalPrice,
      ];
}
