
import '../../domain/entities/product.dart';
import '../../domain/entities/products.dart';

class ProductsModel extends Products {
  List<ProductModel>? productsList;
  Default? defaultObject;
  bool viewMore;

  ProductsModel({this.productsList, this.defaultObject, this.viewMore = false})
      : super(
          products: productsList,
          noProductsObject: defaultObject,
        );

  factory ProductsModel.fromJson(Map<String, dynamic> json) {
    List data = json["items"];
    List<ProductModel> products = data.map((e) {
      return ProductModel.fromJson(e);
    }).toList();

    ProductsModel productsModel = ProductsModel(
        productsList: products,
        defaultObject: null,
        viewMore: json["next_num"] != null);

    return productsModel;
  }
}

class ProductModel extends Product {
  String? createdBy;
  @override
  String? nameAr;
  @override
  String? nameEn;
  @override
  String? subTitleAr;
  @override
  String? subTitleEn;
  @override
  String backgroundColorLight;
  @override
  String backgroundColorDark;
  @override
  int? id;
  String? creation;
  @override
  String? applicationName;
  String? descriptionEn;
  String? action;
  String? countryCode;
  String? descriptionAr;
  @override
  String? image;
  String? status;
  @override
  String? originImage;
  @override
  bool? isHtml;
  @override
  String? htmlContent;
  @override
  String? title;
  @override
  String? goto;

  ProductModel(
      {this.createdBy,
      this.nameAr,
      this.nameEn,
      this.subTitleAr,
      this.subTitleEn,
      this.backgroundColorDark = "0xff000000",
      this.backgroundColorLight = "0xffffffff",
      this.id,
      this.creation,
      this.descriptionEn,
      this.action,
      this.countryCode,
      this.descriptionAr,
      this.image,
      this.status,
      this.originImage,
      this.applicationName,
      this.htmlContent,
      this.isHtml,
      this.goto,
      this.title})
      : super(
            id: id,
            nameEn: nameEn,
            nameAr: nameAr,
            subTitleAr: subTitleAr,
            subTitleEn: subTitleEn,
            image: image,
            isComingSoon: status == "Soon" ? true : false,
            url: action,
            isActive: status == "Inactive" ? false : true,
            backgroundColorLight: "#F7F7ED",
            backgroundColorDark: "#000000",
            originImage: originImage,
            applicationName: applicationName,
            htmlContent: htmlContent,
            isHtml: isHtml,
            title: title);

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
        createdBy: json['created_by'],
        nameAr: json['name_ar'],
        nameEn: json['name_en'],
        subTitleAr: json['subtitle_ar'],
        subTitleEn: json['subtitle_en'],
        backgroundColorLight: json['bg_color_light'] ?? '#F7F7ED',
        backgroundColorDark: json['bg_color_dark'] ?? '#000000',
        id: json['id'],
        creation: json['creation'],
        applicationName: json['application_name'],
        descriptionEn: json['description_en'],
        action: json['action'],
        countryCode: json['country_code'],
        descriptionAr: json['description_ar'],
        image: json['image'],
        status: json['status'],
        originImage: json['origin_image'],
        htmlContent: json["html_content"],
        title: json["title"],
        goto: json["goto"],
        isHtml:
            json["product_content"].toString().toLowerCase().trim() == "html");
  }
}

class Default extends NoProductsObject {
  String? nameEn;
  String? nameAr;
  String? descriptionEn;
  String? descriptionAr;
  @override
  String? image;
  String? action;
  String? status;
  String? countryCode;
  String? applicationName;

  Default({
    this.nameEn,
    this.nameAr,
    this.descriptionEn,
    this.descriptionAr,
    this.image,
    this.action,
    this.status,
    this.countryCode,
    this.applicationName,
  }) : super(
          title: nameEn,
          message: descriptionEn,
          hasAction: action == null ? false : true,
          image: image,
        );

  Default.fromJson(Map<String, dynamic> json) {
    nameEn = json['name_en'];
    nameAr = json['name_ar'];
    descriptionEn = json['description_en'];
    descriptionAr = json['description_ar'];
    image = json['image'];
    action = json['action'];
    status = json['status'];
    countryCode = json['country_code'];
  }
}
