import '../../../../core/util/general_helper.dart';
import '../../domain/entities/withdraw_confirmation.dart';

class WithdrawConfirmationModel extends WithdrawConfirmation {
  @override
  final String? message;
  @override
  final String? sentMessage;
  @override
  final String? sentAmount;
  @override
  final String? receivedMessage;
  @override
  final String? receivedAmount;
  @override
  final String? feesMessage;
  @override
  final String? fees;
  @override
  final List<WithdrawStepModel>? steps;
  @override
  final String? countryCode;
  @override
  final String? currency;
  @override
  final String? status;
  @override
  final String? creationDate;
  @override
  final Map<dynamic, dynamic>? data;

  //"approved_datetime": null,
  //     "canceled_datetime": "2024-10-30 13:20",
  //     "rejected_datetime": null,

  @override
  final String? approvedDate;
  @override
  final String? canceledDate;
  @override
  final String? rejectedDate;
  @override
  final String? messageNote;
  @override
  final String? title;
  @override
  final String? statusKey;

  const WithdrawConfirmationModel({
    required this.message,
    required this.sentMessage,
    required this.sentAmount,
    required this.receivedMessage,
    required this.receivedAmount,
    required this.feesMessage,
    required this.fees,
    required this.steps,
    required this.countryCode,
    required this.currency,
    this.creationDate,
    this.approvedDate,
    this.canceledDate,
    this.rejectedDate,
    this.status,
    this.messageNote,
    this.title,
    this.statusKey,
    required this.data,
  }) : super(
          message: message,
          sentMessage: sentMessage,
          sentAmount: sentAmount,
          receivedMessage: receivedMessage,
          receivedAmount: receivedAmount,
          feesMessage: feesMessage,
          fees: fees,
          steps: steps,
          countryCode: countryCode,
          currency: currency,
          status: status,
          creationDate: creationDate,
          approvedDate: approvedDate,
          canceledDate: canceledDate,
          rejectedDate: rejectedDate,
          data: data,
          messageNotes: messageNote,
          title: title,
          statusKey: statusKey,
        );

  // Factory constructor to parse JSON
  factory WithdrawConfirmationModel.fromJson(Map<String, dynamic> json) {
    return WithdrawConfirmationModel(
      title: json['title'],
      message: json['message'],
      messageNote: json['message_note'],
      creationDate: json['creation'],
      status: json['status'],
      statusKey: json['status_key'],
      sentMessage: json['sent_message'],
      sentAmount: json['sent_amount'] is num
          ? toDigitNumber2(json['sent_amount'])
          : json['sent_amount'],
      receivedMessage: json['received_message'],
      receivedAmount: json['received_amount'] is num
          ? toDigitNumber2(json['received_amount'])
          : json['received_amount'],
      feesMessage: json['fees_message'],
      fees: json['fees'] is num ? toDigitNumber2(json['fees']) : json['fees'],
      steps: json['steps'] != null
          ? List<WithdrawStepModel>.from(
              json['steps'].map((step) => WithdrawStepModel.fromJson(step)))
          : null,
      countryCode: json['country_code'],
      currency: json['currency'],
      approvedDate: json['approved_datetime'],
      canceledDate: json['canceled_datetime'],
      rejectedDate: json['rejected_datetime'],
      data: json['transaction_details'],
    );
  }
}

// Class to handle each step in the steps list
class WithdrawStepModel extends WithdrawStep {
  @override
  final String? icon;
  @override
  final String? message;
  @override
  final bool? isClickable;
  @override
  final String? url;


  const WithdrawStepModel({
    required this.icon,
    required this.message,
    required this.isClickable,
    required this.url,
  }) : super(
          icon: icon,
          message: message,
          isClickable: isClickable,
          url: url,
        );

  // Factory constructor to parse JSON for a step
  factory WithdrawStepModel.fromJson(Map<String, dynamic> json) {
    return WithdrawStepModel(
      icon: json['icon'],
      message: json['message'],
      isClickable: json['is_clickable'],
      url: json['url'] ,
    );
  }
}
