import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:thrivve/app_lifecycle_managment.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/binding/init_binding.dart';
import 'package:thrivve/core/controller/language_controller.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/flavor/flavor_config.dart';
import 'package:thrivve/core/notification/firebase_options/dev_firebase_options.dart';
import 'package:thrivve/core/notification/firebase_options/prod_firebase_options.dart';
import 'package:thrivve/core/notification/firebase_options/stg_firebase_options.dart';
import 'package:thrivve/core/remote_config/init_remote_config.dart';
import 'package:thrivve/core/services/config_service.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/theme/theme_controller.dart';
import 'package:thrivve/core/util/const.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/static_var.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/firebase_service.dart';
import 'package:thrivve/my_blog_delegate.dart';

import 'core/app_routes.dart';
import 'core/injection_container.dart' as di;
import 'core/injection_container.dart';
import 'core/lang/translation.dart';
import 'core/theme/app_custom_theme.dart';

Future<void> main() async {
  await initializeApp();
}

Future<void> initializeApp({
  Flavor flavor = Flavor.prod,
  Color color = Colors.blue,
  bool testMode = false,
  Widget? testWidget,
}) async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);
  }
  await di.init(enableProxy: flavor != Flavor.prod);

  await FlutterBranchSdk.init(enableLogging: false, disableTracking: false);
  // FlutterBranchSdk.validateSDKIntegration();

  await FirebaseService.getInstance(
    options: flavor == Flavor.prod
        ? ProdDefaultFirebaseOptions.currentPlatform
        : flavor == Flavor.stg
            ? StgDefaultFirebaseOptions.currentPlatform
            : DevDefaultFirebaseOptions.currentPlatform,
  );

  // Plugin must be initialized before using
  await FlutterDownloader.initialize(
      debug: false,
      // optional: set to false to disable printing logs to console (default: true)
      ignoreSsl:
          false // option: set to false to disable working with http links (default: false)
      );

  await RemoteConfigService.instance.initialize();
  FlavorConfig(flavor: flavor);

  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  /// third di definition

  await ConfigService.initialize(flavor.name);
  Bloc.observer = MyBlocObserver();
  StaticVar.testWidget = testWidget;
  StaticVar.testMode = testMode;
  Get.put<LanguageController>(
    LanguageController(
      userSecureDataSource: getIt(),
    ),
    permanent: true,
  );

  await initializeDateFormatting("en_US", null).then((_) async {
    // Initialize Sentry for staging and production builds
    if (flavor == Flavor.stg || flavor == Flavor.prod) {
      await SentryService.instance.init(
        (options) {
          options.dsn = ConfigService.sentryDsn;
          options.sendDefaultPii = true;
          options.tracesSampleRate = ConfigService.sentrySampleRate;
          options.enableNativeCrashHandling = true;
          options.enableAutoSessionTracking = true;
          options.enableAutoPerformanceTracing = true;
          options.enableUserInteractionTracing = true;
          options.enableAppHangTracking = true;
          options.debug = flavor == Flavor.stg; // Enable debug logs only for staging
        },
        appRunner: () => runApp(
          const MyApp(),
        ),
      );
    } else {
      runApp(
        const MyApp(),
      );
    }
  });
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    AppLifecycleHandler().init();
    getIt.get<SharedPreferences>().setBool("pin_shown", false);
    getIt.get<SharedPreferences>().setBool("cold_open", true);
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 821),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return Obx(() {
          return MultiBlocProvider(
            providers: [
              BlocProvider<DashboardBloc>(
                create: (context) => getIt<DashboardBloc>(),
              ),
              BlocProvider<MainHomeBloc>(
                create: (context) => getIt<MainHomeBloc>(),
              ),
            ],
            child: GetMaterialApp(
              home: StaticVar.testMode ? StaticVar.testWidget : null,
              navigatorObservers: [
                FirebaseAnalyticsObserver(
                    analytics: getIt<FirebaseAnalytics>()),
                SentryNavigatorObserver(),
              ],
              debugShowCheckedModeBanner: false,
              initialRoute: AppRoutes.splash,
              routingCallback: (routing) {
                if (routing?.current != null) {
                  getIt<IAnalyticsLogger>().logScreenView(
                      screenName: routing?.current ?? "",
                      screenClass: routing?.current ?? "");
                }
              },
              getPages: AppRoutes.routes,
              translations: Translation(),
              locale: Locale(findLanguage()),
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              fallbackLocale: Locale('ar'),
              supportedLocales: const [
                Locale(valueEnLanguage),
                Locale(valueArLanguage),
              ],
              themeMode: Get.find<ThemeController>().themeMode.value,
              darkTheme: getDarkTheme(deviceLocale: Get.locale?.languageCode),
              theme: getLightTheme(deviceLocale: Get.locale?.languageCode),
              initialBinding: InitialBinding(),
            ),
          );
        });
      },
    );
  }
}
