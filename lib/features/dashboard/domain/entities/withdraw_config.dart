import 'package:equatable/equatable.dart';

class WithdrawConfig extends Equatable {
  final double? maxValuePerc;
  final double? minAmount;
  final double? increaseByValue;

  const WithdrawConfig({
    required this.maxValuePerc,
    required this.minAmount,
    required this.increaseByValue,
  });

  @override
  List<Object?> get props => [
        maxValuePerc,
        minAmount,
        increaseByValue,
      ];
}
