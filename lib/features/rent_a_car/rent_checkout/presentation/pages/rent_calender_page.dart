import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/widget/custom_button_widget.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/calendar_day_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/calendar_month_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_input_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/kyc_type_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/enum/step_rent_car_enum.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/manager/rent_car_checkout_controller.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/presentation/widgets/checkout_step_tile.dart';
import 'package:thrivve/generated/assets.dart';

class RentCalendarPage extends GetView<RentCarCheckoutController> {
  const RentCalendarPage({super.key});

  void _trackUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'User interaction: $action',
      category: 'user_interaction',
      data: data,
    );
  }

  void _trackNavigationAction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'Navigation action: $action',
      category: 'navigation',
      data: {
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        ...?data,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<String> weekDays = [
      'day_sun'.tr,
      'day_mon'.tr,
      'day_tus'.tr,
      'day_wen'.tr,
      'day_th'.tr,
      'day_f'.tr,
      'day_s'.tr
    ];

    return WillPopScope(
      onWillPop: () async {
        _trackNavigationAction('back_button_pressed', data: {
          'selected_date': controller.selectedDate.value,
          'is_radio_type': controller.isRadioType,
        });
        return true;
      },
      child: Scaffold(
        backgroundColor: context.backgroundColor,
        body: SafeArea(
          child: NestedScrollView(
            headerSliverBuilder: (BuildContext context, bool innerBoxScrolled) {
              return <Widget>[
                _buildAppBar(context),
              ];
            },
            body: Column(
              children: [
                _buildWeekDaysHeader(context, weekDays),
                const SizedBox(height: 4),
                Expanded(
                  child: _buildCalendarContent(context),
                ),
                _buildConfirmButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // App Bar Section
  SliverAppBar _buildAppBar(BuildContext context) {
    return SliverAppBar(
      leadingWidth: 60.w,
      leading: _buildCloseButton(context),
      centerTitle: true,
      automaticallyImplyLeading: false,
      expandedHeight: controller.isRadioType ? 140.h : 120.h,
      floating: false,
      forceElevated: true,
      elevation: 0,
      excludeHeaderSemantics: true,
      pinned: true,
      surfaceTintColor: Colors.transparent,
      flexibleSpace: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final bool isCollapsed = constraints.maxHeight <= kToolbarHeight + 10;
          return FlexibleSpaceBar(
            background: Container(
              color: context.backgroundColor,
            ),
            expandedTitleScale: 1,
            titlePadding: isCollapsed
                ? EdgeInsetsDirectional.zero
                : EdgeInsetsDirectional.only(
                    start: 16.w,
                    end: 16.w,
                    top: 40.h,
                  ),
            centerTitle: isCollapsed,
            title: Obx(() => Align(
                  alignment: isCollapsed
                      ? Alignment.center
                      : (Directionality.of(context) == TextDirection.rtl
                          ? Alignment.centerRight
                          : Alignment.centerLeft),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomTextWidget(
                        paddingStart: 0,
                        fontWeight: FontWeight.bold,
                        textAlign: TextAlign.start,
                        title: controller.isRadioType
                            ? 'delivery_date'.tr
                            : 'choose_pick_up_time'.tr,
                        size: 20,
                      ),
                      if (controller.isRadioType) SizedBox(height: 10.h),
                      if (isCollapsed.inverted && controller.isRadioType)
                        CustomTextWidget(
                          title: 'choose_delivery_date'.tr,
                          size: 12,
                          textAlign: TextAlign.start,
                          color: context.black,
                        ),
                    ],
                  ),
                )),
            collapseMode: CollapseMode.parallax,
          );
        },
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: 16.w),
        Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: InkWell(
            onTap: () {
              _trackNavigationAction('close_button_clicked', data: {
                'selected_date': controller.isRadioType
                    ? controller.selectedRadioDateValue.value
                    : controller.selectedDate.value,
                'is_radio_type': controller.isRadioType,
              });
              Get.back();
            },
            child: Icon(
              Icons.close,
              color: context.black,
              size: 20.w,
            ),
          ),
        ),
      ],
    );
  }

  // Week Days Header Section
  Widget _buildWeekDaysHeader(BuildContext context, List<String> weekDays) {
    return Obx(
      () => Visibility(
        visible: controller.isRadioType.inverted,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 20.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: weekDays
                .map((d) => Expanded(
                      child: Center(
                        child: CustomTextWidget(
                          title: d,
                          color: context.black,
                          fontWeight: FontWeight.w400,
                          size: 16,
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }

  // Calendar Content Section
  Widget _buildCalendarContent(BuildContext context) {
    return Obx(() {
      if (controller.isLoadingCalendar.value) {
        return SingleChildScrollView(child: _buildLoadingWidget(context));
      }

      if (controller.calendarError.value.isNotEmpty) {
        return Center(
          child: CustomTextWidget(
            title: controller.calendarError.value,
            color: Colors.red,
          ),
        );
      }

      final calendarResponse = controller.calendarResponse.value;
      if (calendarResponse == null || calendarResponse.months == null) {
        return SizedBox.shrink();
      }

      return Obx(() {
        if (controller.isRadioType) {
          return _buildRadioDayList(context, calendarResponse.months ?? []);
        }
        return SingleChildScrollView(
          child: Column(
            children: (calendarResponse.months ?? [])
                .map((month) => _buildMonthSection(month, context))
                .toList(),
          ),
        );
      });
    });
  }

  // Calendar Grid Section
  Widget _buildMonthSection(CalendarMonthEntity month, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: EdgeInsets.only(
            right: 15.w,
            left: 15.w,
            top: 16,
            bottom: 8,
          ),
          child: Align(
            alignment: Alignment.centerRight,
            child: CustomTextWidget(
              title: "${month.month} ${month.year}",
              color: context.black,
              fontWeight: FontWeight.w600,
              size: 16,
            ),
          ),
        ),
        if (month.days != null) _buildDaysGrid(month.days ?? [], context),
      ],
    );
  }

  Widget _buildDaysGrid(List<CalendarDayEntity> days, BuildContext context) {
    days.sort((a, b) => (a.date ?? '').compareTo(b.date ?? ''));
    List<Widget> rows = [];
    int firstWeekday = 0;
    if (days.isNotEmpty && days[0].date != null) {
      final firstDate = DateTime.tryParse(days[0].date!);
      if (firstDate != null) {
        firstWeekday = firstDate.weekday % 7;
      }
    }
    List<Widget> week =
        List.generate(7, (_) => const Expanded(child: SizedBox()));
    int colIndex = firstWeekday;
    for (final day in days) {
      final isSelected = controller.selectedDate.value == day.date;
      String dayNumber = '';
      if (day.date != null) {
        final parts = day.date!.split('-');
        if (parts.length == 3) {
          dayNumber = parts[2].replaceAll(RegExp(r'^0+'), '');
        }
      }
      week[colIndex] = _buildDayCell(day, dayNumber, isSelected, context);
      colIndex++;
      if (colIndex == 7) {
        rows.add(
          Row(
            children: week,
          ),
        );
        week = List.generate(
          7,
          (_) => const Expanded(
            child: SizedBox(),
          ),
        );
        colIndex = 0;
      }
    }
    if (week.any((w) => w is! SizedBox)) {
      rows.add(Row(children: week));
    }
    return Column(children: rows).paddingSymmetric(
      horizontal: 15.w,
    );
  }

  Widget _buildDayCell(CalendarDayEntity day, String dayNumber, bool isSelected,
      BuildContext context) {
    var color = isSelected && day.isCurrent != true
        ? BoxDecoration(
            color: context.appPrimaryColor,
            borderRadius: BorderRadius.circular(12),
          )
        : null;
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          splashColor: context.appPrimaryColor.withValues(alpha: 0.2),
          highlightColor: context.appPrimaryColor.withValues(alpha: 0.2),
          hoverColor: context.appPrimaryColor.withValues(alpha: 0.2),
          onTap: () {
            if (day.isDisabled == true || day.isCurrent == true) {
              _trackUserInteraction('day_cell_clicked', data: {
                'date': day.date,
                'is_disabled': day.isDisabled,
                'is_current': day.isCurrent,
                'day_number': dayNumber,
              });
              return;
            }
            _trackUserInteraction('day_cell_clicked', data: {
              'date': day.date,
              'is_disabled': day.isDisabled,
              'is_current': day.isCurrent,
              'day_number': dayNumber,
              'previous_selection': controller.selectedDate.value,
            });
            controller.selectDate(day.date);
          },
          child: Container(
            margin: EdgeInsetsDirectional.only(
              bottom: 5.h,
            ),
            padding: EdgeInsetsDirectional.symmetric(
              vertical: 12.h,
              horizontal: 12.h,
            ),
            decoration: color,
            alignment: Alignment.center,
            child: CustomTextWidget(
              title: dayNumber,
              color: day.isCurrent == true
                  ? Colors.red
                  : day.isDisabled == true
                      ? context.black.withValues(alpha: 0.2)
                      : isSelected
                          ? Colors.white
                          : context.black,
              fontWeight: FontWeight.w600,
              size: 16,
            ),
          ),
        ),
      ),
    );
  }

  // Radio List Section
  Widget _buildRadioDayList(
      BuildContext context, List<CalendarMonthEntity> months) {
    final List<CalendarDayEntity> allDays =
        months.where((m) => m.days != null).expand((m) => m.days!).toList();

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: allDays.length,
            separatorBuilder: (_, __) => Padding(
              padding: EdgeInsetsDirectional.only(
                start: 16.w,
                end: 16.w,
              ),
              child: Divider(height: 1, color: context.borderLeaseColor),
            ),
            itemBuilder: (context, idx) =>
                _buildRadioDayItem(allDays[idx], context),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioDayItem(CalendarDayEntity day, BuildContext context) {
    return Obx(() {
      final isSelected =
          controller.selectedRadioDateValue.value == day.dayValue;

      return InkWell(
        onTap: day.isDisabled == true
            ? () {
                _trackUserInteraction('radio_day_clicked', data: {
                  'date': day.date,
                  'is_disabled': true,
                  'day_name': day.day,
                });
              }
            : () {
                _trackUserInteraction('radio_day_clicked', data: {
                  'date': day.date,
                  'is_disabled': false,
                  'day_name': day.day,
                  'previous_selection': controller.selectedRadioDateValue.value,
                });
                controller.selectRadioDate(day.dayValue, day.date);
              },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
          child: Row(
            children: [
              SvgPicture.asset(
                  isSelected
                      ? Assets.rentACarRentSelectionDate
                      : Assets.rentACarRentNotSelectionDate,
                  width: 27.w,
                  height: 27.w,
                  colorFilter: ColorFilter.mode(
                      day.isDisabled == true
                          ? context.black.withValues(alpha: 0.2)
                          : context.outlineButtonColor,
                      BlendMode.srcIn)),
              SizedBox(width: 12.w),
              Text(
                day.day ?? '',
                style: TextStyle(
                  color: day.isDisabled == true
                      ? context.black.withValues(alpha: 0.2)
                      : context.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
              ),
              Spacer(),
              Text(
                day.date ?? '',
                style: TextStyle(
                  color: day.isDisabled == true
                      ? context.black.withValues(alpha: 0.2)
                      : context.black,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  fontSize: 15,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  // Loading Widget Section
  Widget _buildLoadingWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Column(
        children: List.generate(
          2,
          (monthIdx) => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                width: 120.w,
                height: 18.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: context.borderAddBranch,
                ),
              ),
              Shimmer.fromColors(
                baseColor: context.borderAddBranch,
                highlightColor: context.appBackgroundColor,
                enabled: true,
                child: Column(
                  children: List.generate(
                    5,
                    (rowIdx) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: List.generate(
                          7,
                          (colIdx) => Expanded(
                            child: Container(
                              margin: const EdgeInsets.symmetric(horizontal: 2),
                              height: 36.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: context.borderAddBranch,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 24.h),
            ],
          ),
        ),
      ),
    );
  }

  // Confirm Button Section
  Widget _buildConfirmButton(BuildContext context) {
    return Column(
      children: [
        Divider(
          color: context.black.withValues(alpha: 0.08),
          thickness: 4.h,
          height: 4.h,
        ),
        SizedBox(height: 16.h),
        Obx(
          () => CustomButton(
            isLoading: controller.isLoadingKYC,
            margin: EdgeInsetsDirectional.only(
              start: 16.w,
              end: 16.w,
            ),
            key: ValueKey('confirm_date'),
            enabled:
                controller.pickUpDateStatus.value == CheckoutStepStatus.done,
            text: 'confirm_date'.tr,
            onPressed: () {
              _trackUserInteraction('confirm_date_clicked', data: {
                'selected_date': controller.isRadioType
                    ? controller.selectedRadioDateValue.value
                    : controller.selectedDate.value,
                'is_radio_type': controller.isRadioType,
                'is_loading': controller.isLoadingKYC,
                'pickup_status': controller.pickUpDateStatus.value.toString(),
              });
              final input = KYCInputEntity(
                type: KYCTypeEnum.pickUpDate,
                pickUpDate: controller.isRadioType
                    ? controller.selectedRadioDateForApi
                    : controller.selectedDate.value ?? '',
              );
              controller.submitKYC(input, StepRentCarEnum.pickup_date);
            },
            colorText: Colors.white,
          ),
        ),
        SizedBox(height: 5.h),
      ],
    );
  }
}
