class KYCResponseEntity {
  final String? dateOfBirth;
  final int? milageId;
  final int? paymentMethodId;
  final int? nationalId;
  final int? customerId;
  final int? cityId;
  final String? drivingLicenceUrl;
  final String? identityDocumentUrl;
  final String? nationality;
  final int? commitmentMonths;
  final String? pickupDate;
  final int? vehicleId;
  final int? insuranceId;
  final String? campaignName;
  final String? stepName;
  final String? captainName;
  final int? vehiclePricingId;

  const KYCResponseEntity({
    this.dateOfBirth,
    this.milageId,
    this.paymentMethodId,
    this.nationalId,
    this.customerId,
    this.cityId,
    this.drivingLicenceUrl,
    this.identityDocumentUrl,
    this.nationality,
    this.commitmentMonths,
    this.pickupDate,
    this.vehicleId,
    this.insuranceId,
    this.campaignName,
    this.stepName,
    this.captainName,
    this.vehiclePricingId,
  });
}

class KYCDataEntity {
  final String? dateOfBirth;
  final int? milageId;
  final int? paymentMethodId;
  final int? nationalId;
  final int? customerId;
  final int? cityId;
  final String? drivingLicenceUrl;
  final String? identityDocumentUrl;
  final String? nationality;
  final int? commitmentMonths;
  final String? pickupDate;
  final int? vehicleId;
  final int? insuranceId;
  final String? campaignName;
  final String? stepName;
  final String? captainName;
  final int? vehiclePricingId;

  const KYCDataEntity({
    this.dateOfBirth,
    this.milageId,
    this.paymentMethodId,
    this.nationalId,
    this.customerId,
    this.cityId,
    this.drivingLicenceUrl,
    this.identityDocumentUrl,
    this.nationality,
    this.commitmentMonths,
    this.pickupDate,
    this.vehicleId,
    this.insuranceId,
    this.campaignName,
    this.stepName,
    this.captainName,
    this.vehiclePricingId,
  });
} 