import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_support_url_use_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/widgets/validators.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/use_cases/link_uber_with_thrivve_use_case.dart';
import 'package:thrivve/generated/assets.dart';

class LinkUberAccountController extends GetxController {
  final LinkUberWithThrivveUseCase linkUberWithThrivveUseCase;
  final GetSupportUrlUseCase getSupportUrlUseCase;
  final IShowBottomSheetHelper iShowBottomSheetHelper;
  LinkUberAccountController(this.linkUberWithThrivveUseCase,
      this.iShowBottomSheetHelper, this.getSupportUrlUseCase);
  RxnString haveUberAccount = RxnString();
  RxBool showPage = false.obs;
  RxBool hasAccount = false.obs;
  TextEditingController mailController = TextEditingController();
  TextEditingController mobileController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey();
  String? supportWhats;
  getSupportInformation() async {
    showLoaderDialog(Get.context!);
    final result = await getSupportUrlUseCase.call(NoParams());
    result?.fold((error) {
      dismissLoaderDialog(Get.context!);
      return _handleFailure(error);
    }, (data) {
      dismissLoaderDialog(Get.context!);
      Get.back();
      openWhatsApp(url: data ?? "");
    });
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    addListeners();
  }

  addListeners() {
    mailController.addListener(_validateForm);
    mobileController.addListener(_validateForm);
  }

  RxBool isNextButtonEnabled = false.obs;
  validateForm() => _validateForm();
  void _validateForm() {
    isNextButtonEnabled.value =
        Validators.validateEmail(mailController.text) == null &&
            Validators.validatePhoneNumber(mobileController.text) == null;
  }

  linkUberWithThrive() async {
    showLoaderDialog(Get.context!);
    final result = await linkUberWithThrivveUseCase.call(
        LinkUberWithThrivveRequestParamsModel(
            hasUberAccount: hasAccount.value,
            uberAccountEmail: mailController.text,
            uberAccountPhone: "966${mobileController.text}"));
    result?.fold((error) {
      dismissLoaderDialog(Get.context!);
      Get.back();
      return _handleFailure(error);
    }, (data) {
      dismissLoaderDialog(Get.context!);
      Get.back();
      showCustomBottomSheet(Get.context!,
          title: "sucees_sent",
          message: Text(
            "order_under_review".tr,
            textAlign: TextAlign.center,
            style:
                TextStyle(fontSize: 11.sp, color: Get.context!.hintTextColor2),
          ),
          iconImage: Assets.thrivvePhotosSuccess, primaryButtonAction: () {
        reinitializeBloc();
        Get.offAllNamed(AppRoutes.homePage);
      });
    });
  }

  _handleFailure(Failure failure) {
    final error = mapFailureToMessage(failure);
    errorSnackBar(context: Get.context!, title: "err".tr, message: error);
  }

  submitForm() {
    showCustomBottomSheet(Get.context!,
        title: "send",
        message: Text(
          "submit_uber_alert_message".tr,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 11.sp, color: Get.context!.hintTextColor2),
        ),
        iconImage: Assets.thrivvePhotosInfoUber,
        primaryButtonText: "yes".tr,
        isSecondaryButtonVisible: true,
        secondaryButtonText: "are_you_sure_to_cancel_no".tr,
        primaryButtonAction: () {
      linkUberWithThrive();
    });
  }
}
