import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io' as io;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:thrivve/app_lifecycle_managment.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/notifications_fcm/i_firebase_messaging.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/features/dashboard/presentation/manager/dashboard_bloc.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/notifications/domain/use_cases/get_transaction_status_use_case.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/presentation/pages/kyc_argument.dart';
import 'package:thrivve/features/uber_flow/progress_step_uber_flow/presentation/arguments/progress_page_arguments.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/presentation/pages/uber_application_review_params.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/presentation/arguments/select_vehicles_page_arguments.dart';

class NotificationData {
  String screenName;
  dynamic arguments;
  NotificationData({required this.screenName, this.arguments});
}

class NotificationChannelConfig {
  final String id;
  final String name;
  final String description;
  final String sound;
  final Importance importance;

  const NotificationChannelConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.sound,
    this.importance = Importance.high,
  });
}

class FireBaseMessagingImpl extends IFirebaseMessaging {
  FireBaseMessagingImpl._();
  static FireBaseMessagingImpl fireBaseMessagingImpl =
      FireBaseMessagingImpl._();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Channel configurations

  @override
  Future<void> configureMessaging() async {
    try {
      log('Configuring Firebase Messaging');

      // Request permissions first
      await _requestNotificationPermissions();

      await setUpChannelsId();
      await Future.wait([
        setUpFlutterLocalNotification(),
        setUpForGroundNotification(),
        getTokenFirebase(),
        handleBackGroundNotifications(),
        handleForGroundNotifications(),
        onChangedToken(),
      ]);

      log('Firebase Messaging configured successfully');
    } catch (e, stackTrace) {
      log('Error configuring Firebase Messaging: $e\n$stackTrace');
    }
  }

  Future<void> _requestNotificationPermissions() async {
    try {
      if (io.Platform.isAndroid) {
        final androidImplementation = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();
        await androidImplementation?.requestNotificationsPermission();
      } else {
        await flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: false,
              badge: false,
              sound: false,
            );
      }
    } catch (e) {
      log('Error requesting permissions: $e');
    }
  }

  @override
  Future<void> handleForGroundNotifications() async {
    FirebaseMessaging.onMessage.listen((RemoteMessage? message) async {
      try {
        Map? map = message?.data;
        bool refreshBalance = map?["refresh_balance"] == true;
        if (refreshBalance) {
          getIt<MainHomeBloc>().add(const GetBalanceEvent());
        }
        await _showNotification(message: message);
      } catch (e, stackTrace) {
        log('Error handling foreground notification: $e\n$stackTrace');
      }
    });
  }

  Future<void> _showNotification({
    RemoteMessage? message,
  }) async {
    if (io.Platform.isAndroid.inverted) return;
    Map? data = message?.data;
    try {
      await flutterLocalNotificationsPlugin.show(
        message.hashCode,
        message?.notification?.title,
        message?.notification?.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            message?.notification?.android?.channelId ??
                "default_channel_id_default_sound",
            'Default Notifications',
            channelDescription: 'Channel for default notifications',
            icon: '@mipmap/not',
            importance: Importance.high,
            playSound: true,
            priority: Priority.max,
            styleInformation:
                BigTextStyleInformation(message?.notification?.body ?? ''),
            sound: RawResourceAndroidNotificationSound(
                data?["sound"] ?? 'default_sound'),
          ),
          iOS: DarwinNotificationDetails(
              sound: data?["sound"] ?? 'default_sound'),
        ),
        payload: jsonEncode(data),
      );
      log('Notification shown successfully');
    } catch (e) {
      log('Error showing notification: $e');
    }
  }

  @override
  Future<void> handleBackGroundNotifications() async {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      try {
        Map? map = message.data;
        bool refreshBalance = map["refresh_balance"] == true;
        if (refreshBalance) {
          getIt<MainHomeBloc>().add(const GetBalanceEvent());
        }
        await _handleNotificationOpen(message);
      } catch (e) {
        log('Error handling background notification: $e');
      }
    });
  }

  @override
  Future<void> handleNotificationsWhenAppClosed() async {
    try {
      final message = await FirebaseMessaging.instance.getInitialMessage();

      if (message != null) {
        await _handleNotificationOpen(message);
      }
    } catch (e) {
      log('Error handling terminated state notification: $e');
    }
  }

  Future<void> _handleNotificationOpen(RemoteMessage message) async {
    final data = message.data;
    await _handleNotificationNavigation(data);
  }

  Future<void> _handleNotificationNavigation(Map<dynamic, dynamic> data) async {
    NotificationData notificationData = await getNotificationScreenArgument(
        data["screen_name"], data["arguments"]);
    String? screenName = notificationData.screenName;
    dynamic arguments = notificationData.arguments;

    try {
      final isLogin = await getIt<UserSecureDataSource>().isLogin();
      if (isLogin) {
        if (AppLifecycleHandler.instance.backgroundTime != null &&
            DateTime.now()
                    .difference(AppLifecycleHandler.instance.backgroundTime!) >=
                AppLifecycleHandler.lockDuration) {
          // Store the pending route to navigate after PIN verification
          AppLifecycleHandler.pendingRoute = screenName;
          AppLifecycleHandler.arguments = arguments;

          // Redirect to PIN first
          await AppLifecycleHandler.instance.navigateToPinCode();
        } else {
          if (screenName != "dont_navigate" && arguments != "dont_navigate") {
            Get.toNamed(screenName, arguments: arguments);
          } else if (arguments == "dont_navigate") {
            Get.toNamed(AppRoutes.notification);
          } else {
            Get.toNamed(AppRoutes.notification);
          }
        }
      }
    } catch (e) {
      log('Error handling notification type: $e');
    }
  }

  Map<String, dynamic>? handleNotificationMapArgument(String? jsonString) {
    if (jsonString == null || jsonString.trim().isEmpty) {
      return null;
    }
    jsonString = jsonString.replaceAll("'", "\""); // Fix single quotes
    jsonString = jsonString.replaceAllMapped(RegExp(r'\b(True|False)\b'),
        (match) => match[0]!.toLowerCase()); // Convert True/False

    if (!(jsonString.trim().startsWith('{') &&
        jsonString.trim().endsWith('}'))) {
      return null; // Not a valid JSON object
    }
    final decoded = jsonDecode(jsonString);
    dynamic result = decoded is Map<String, dynamic> ? decoded : null;
    log("result is $result");
    return result;
  }

  Future<NotificationData> getNotificationScreenArgument(
      String? screenNameArg, dynamic args) async {
    String screenName = getScreenName(screenNameArg);

    const Set<String> noArgsScreens = {
      AppRoutes.notification,
      AppRoutes.homePage,
      AppRoutes.bankAccountListPage,
      AppRoutes.personalInfo,
      AppRoutes.productsPage,
      AppRoutes.underProcessingPage,
      AppRoutes.topUpPage,
      AppRoutes.updateMobileScreen,
      AppRoutes.privacyAndSecurity,
      AppRoutes.settingScreen,
      AppRoutes.faqScreen,
      AppRoutes.withdrawPage,
      AppRoutes.profileScreen,
      AppRoutes.personalDetailsScreen,
      AppRoutes.referralScreen,
      AppRoutes.linkUberWithThrivvePage,
      AppRoutes.infoSupplierPage,
      AppRoutes.instructionsUberFlowPage,
      '/ChangePinScreen'
    };

    if (noArgsScreens.contains(screenName)) {
      return NotificationData(screenName: screenName, arguments: null);
    }

    Map? argument = handleNotificationMapArgument(args);

    switch (screenName) {
      case AppRoutes.pinScreen:
        return NotificationData(
            screenName: screenName, arguments: argument ?? {});

      case AppRoutes.identityIInfo:
        return NotificationData(
            screenName: screenName,
            arguments: (argument != null
                ? {"notificationId": argument["notification_id"]}
                : null));

      case AppRoutes.driveToOwn:
        dynamic driveToOwnArg = (argument == null ||
                argument.isEmpty ||
                argument["contract_id"] == null)
            ? "dont_navigate"
            : {"contractId": argument["contract_id"].toString()};
        return NotificationData(
            screenName: screenName, arguments: driveToOwnArg);

      case AppRoutes.transactionsPage:
        dynamic transactionsPageArg = argument != null
            ? {
                "currency": argument["currency"],
                "isAmountVisible": argument["is_amount_visible"]
              }
            : null;
        return NotificationData(
            screenName: screenName, arguments: transactionsPageArg);

      case AppRoutes.pendingTransactionDetailsPage:
        String? id = safeCast<String>(args);
        if (id != null) {
          String? result = await navigateToTransactionStatus(id);
          if (result?.toLowerCase() == "pending") {
            return NotificationData(
                screenName: screenName, arguments: safeCast<String>(args));
          } else if (result?.toLowerCase() == "approved" ||
              result?.toLowerCase() == "rejected") {
            return NotificationData(
                screenName: AppRoutes.transactionDetailsPage,
                arguments: safeCast<String>(args));
          } else {
            return NotificationData(
                screenName: AppRoutes.transactionsPage,
                arguments: {
                  "isAmountVisible": true,
                });
          }
        } else {
          return NotificationData(
              screenName: AppRoutes.transactionsPage,
              arguments: {
                "isAmountVisible": true,
              });
        }

      case AppRoutes.transactionDetailsPage:
        String? id = safeCast<String>(args);
        if (id != null) {
          String? result = await navigateToTransactionStatus(id);
          if (result?.toLowerCase() == "pending") {
            return NotificationData(
                screenName: AppRoutes.pendingTransactionDetailsPage,
                arguments: safeCast<String>(args));
          } else if (result?.toLowerCase() == "approved" ||
              result?.toLowerCase() == "rejected") {
            return NotificationData(
                screenName: AppRoutes.transactionDetailsPage,
                arguments: safeCast<String>(args));
          } else {
            return NotificationData(
                screenName: AppRoutes.transactionsPage,
                arguments: {
                  "isAmountVisible": true, // check this
                });
          }
        } else {
          return NotificationData(
              screenName: AppRoutes.transactionsPage,
              arguments: {
                "isAmountVisible": true,
              });
        }

      case AppRoutes.paymentRequestDonePage:
        return NotificationData(
            screenName: screenName,
            arguments: argument ?? {"message": '', "title": ''});

      case AppRoutes.vehicleDetailsPage:
        return NotificationData(
            screenName: screenName,
            arguments: argument != null
                ? {"carId": argument["car_id"]}
                : "dont_navigate");

      case AppRoutes.selectVehiclesUberFlowPage:
        return NotificationData(
            screenName: screenName,
            arguments: SelectVehiclesPageArguments(
              fromDashboard: true,
              withUberAllDataEntity:
                  getIt<DashboardBloc>().state.dataWorkWithUber,
            ));

      case AppRoutes.KYCUberPage:
        final dashboardState = getIt<DashboardBloc>().state;
        return NotificationData(
            screenName: screenName,
            arguments: KYCPageArguments(
              fromDashboard: true,
              dataWorkWithUber: dashboardState.dataWorkWithUber,
              isOwnCar:
                  dashboardState.dataWorkWithUber?.customerOwnedCar ?? false,
            ));

      case AppRoutes.reviewUperApplicationPage:
        return NotificationData(
            screenName: screenName,
            arguments: UploadReviewArgument(
              fromDashboard: true,
              dataWorkWithUber: getIt<DashboardBloc>().state.dataWorkWithUber,
            ));

      case AppRoutes.progressApplicationPage:
        return NotificationData(
            screenName: screenName,
            arguments: argument == null
                ? "dont_navigate"
                : ProgressPageArgument(
                    applicationId: argument["application_id"]));

      default:
        return NotificationData(screenName: screenName, arguments: args);
    }
  }

  String getScreenName(String? screenName) {
    String routeName = screenName ?? AppRoutes.notification;
    if (!Routes.routeExists(routeName)) {
      routeName = AppRoutes.notification; // Fallback to default route
    }
    if (Get.currentRoute == routeName) {
      routeName = "dont_navigate";
    }
    return routeName;
  }

  Future<String?> navigateToTransactionStatus(String id) async {
    showLoaderDialog(Get.context!);
    final result = await getIt.get<GetTransctionStatusUseCase>().call(id);
    String? resultString;
    result?.fold((error) {
      dismissLoaderDialog(Get.context!);
      resultString = null;
    }, (sucess) {
      dismissLoaderDialog(Get.context!);
      resultString = sucess?.status;
    });
    return resultString;
  }

  @override
  Future<void> setUpFlutterLocalNotification() async {
    try {
      final initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
        onDidReceiveLocalNotification: (id, title, body, payload) async {
          handleLocalNotification(payload);
        },
      );

      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/not');

      final initializationSettings = InitializationSettings(
        iOS: initializationSettingsIOS,
        android: initializationSettingsAndroid,
      );

      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (notificationResponse) async {
          log('Notification response received: ${notificationResponse.payload}');
          handleLocalNotification(notificationResponse.payload);
        },
      );

      log('Flutter Local Notifications initialized successfully');
    } catch (e) {
      log('Error initializing Flutter Local Notifications: $e');
    }
  }

  @override
  Future<void> setUpForGroundNotification() async {
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  @override
  Future<String> getTokenFirebase() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await saveTokenFirebase(token);
        log('FCM Token refreshed: $token');
      }
      return token ?? '';
    } catch (e) {
      log('Error getting FCM token: $e');
      return '';
    }
  }

  @override
  Future<void> saveTokenFirebase(String? token) async {
    if ((token ?? '').isNotEmpty) {
      try {
        await getIt<UserSecureDataSource>().setFcmToken(token);
        log('FCM token saved successfully');
      } catch (e) {
        log('Error saving FCM token: $e');
      }
    }
  }

  @override
  Future<void> onChangedToken() async {
    _firebaseMessaging.onTokenRefresh.listen((token) async {
      log('FCM token refreshed');
      await saveTokenFirebase(token);
    });
  }

  void handleLocalNotification(dynamic payload) {
    if (payload != null && payload.isNotEmpty) {
      try {
        final data = json.decode(payload);
        _handleNotificationNavigation(data);
      } catch (e) {
        log('Error handling local notification: $e');
      }
    }
  }

  @override
  Future<void> deleteToken() async {
    try {
      await _firebaseMessaging.deleteToken();
      await getIt<UserSecureDataSource>().setFcmToken(null);
      log('FCM token deleted successfully');
    } catch (e) {
      log('Error deleting FCM token: $e');
    }
  }

  @override
  Future<void> requestPermission() async {
    await FirebaseMessaging.instance.requestPermission();
  }

  @override
  Future<void> setUpChannelsId() async {
    if (!io.Platform.isAndroid) return;
    List<String> soundFiles = [
      'default_sound',
      'money_1',
      'money_2',
      'money_3',
      'money_4',
      'money_5',
      'money_6',
      'money_7',
      'money_8',
      'money_9',
      'money_10',
    ];

    // Create notification channels dynamically
    for (String sound in soundFiles) {
      AndroidNotificationChannel channel = AndroidNotificationChannel(
        'default_channel_id_$sound', // Channel ID (must be unique)
        'Channel for $sound', // Channel Name
        description: 'Plays $sound.mp3',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound(sound),
        playSound: true,
      );

      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }
}
