import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/data_sources/i_select_vehicles_data_soruce.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/PricingVehiclesModel.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/save_vehicle_step_model.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/vehicle_models.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/save_vehicle_step_input_entity.dart';

class SelectVehiclesDataSourceImpl extends ISelectVehiclesDataSource {
  final ApiClient apiClient;

  SelectVehiclesDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<PricingVehiclesModel>?> getAllPricingVehicles(
      int vehicleId) async {
    final response = await apiClient.request<List<PricingVehiclesModel>>(
      endpoint: ApiSettings.pricingVehiclesUrl
          .replaceAll('vehicleId', vehicleId.toString()),
      fromJson: (json) => listPricingVehiclesModelFromJson(json),
    );
    return response.data;
  }

  @override
  Future<SaveVehicleStepModel?> saveVehicleStepInputEntity(
      SaveVehicleStepInputEntity input) async {
    final response = await apiClient.request<SaveVehicleStepModel?>(
      removeNullValues: true,
      endpoint: ApiSettings.saveVehicleStep,
      method: RequestType.post,
      data: input.toJson(),
      fromJson: (json) => SaveVehicleStepModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<List<VehicleModel?>?> getVehicleModels() async {
    final response = await apiClient.request<List<VehicleModel?>>(
      endpoint: ApiSettings.uberVehicleModels,
      fromJson: (json) => listVehicleModelFromJson(json),
    );
    return response.data;
  }
}
