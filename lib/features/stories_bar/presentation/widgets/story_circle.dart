import 'package:flutter/material.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/stories_bar/data/models/story_model.dart';

class StoryCircle extends StatelessWidget {
  final UserStories userStories;
  final VoidCallback onTap;
  final double size;

  const StoryCircle({
    Key? key,
    required this.userStories,
    required this.onTap,
    this.size = 70.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: size,
            height: size,
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: userStories.hasUnviewedStories
                  ? LinearGradient(
                      colors: [context.appPrimaryColor, Colors.orange],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [Colors.grey, Colors.grey],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
            ),
            child: Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: CircleAvatar(
                backgroundImage: NetworkImage(userStories.userAvatar),
              ),
            ),
          ),
          SizedBox(height: 4),
          SizedBox(
            width: size,
            child: Text(
              userStories.userName,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
}
