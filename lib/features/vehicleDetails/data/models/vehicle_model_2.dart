import '../../domain/entities/feature.dart';
import '../../domain/entities/vehicle.dart';

class VehicleModel2 extends Vehicle {
  final double? rate;
  final String? motorSizeValue;
  final String? totalPriceValue;
  final String? fuelTypeValue;
  final String? vehicleType;
  final String? carNameValue;
  final String? gearTypeValue;
  final String? deliveryDateValue;
  final String? priceNoteValue;
  final List<ImageData>? listOfImages;
  final List<Section>? sections;
  final int? like;
  final ImageData? mainImage;

  VehicleModel2({
    required this.rate,
    required this.motorSizeValue,
    required this.totalPriceValue,
    required this.fuelTypeValue,
    required this.vehicleType,
    required this.listOfImages,
    required this.sections,
    required this.like,
    required this.gearTypeValue,
    required this.carNameValue,
    required this.mainImage,
    required this.deliveryDateValue,
    required this.priceNoteValue,
  }) : super(
          id: '',
          priceNote: priceNoteValue ?? '',
          carName: carNameValue ?? '',
          likes: like ?? 0,
          images: listOfImages != null
              ? listOfImages.map((i) => i.url).toList()
              : [],
          gearType: gearTypeValue ?? '',
          fuelType: fuelTypeValue ?? '',
          motorSize: motorSizeValue ?? '',
          carType: vehicleType ?? '',
          features: sections != null
              ? sections
                  .map((s) => Feature(
                        title: s.title ?? '',
                        features: s.items ?? [],
                      ))
                  .toList()
              : [],
          totalPrice: totalPriceValue ?? '',
          deliveryDate: deliveryDateValue ?? '',
          isLiked: like == 1 ? true : false,
        );

  factory VehicleModel2.fromJson(Map<String, dynamic> json) {
    return VehicleModel2(
      rate: json['rate']?.toDouble(),
      priceNoteValue: json['price_note'],
      motorSizeValue: json['motor_size'],
      totalPriceValue: json['total_price'],
      fuelTypeValue: json['fuel_type'],
      vehicleType: json['vehicle_type'],
      carNameValue: json['car_name'],
      deliveryDateValue: json['delivery_date'],
      gearTypeValue: json['gear_type'],
      listOfImages: json['images'] != null
          ? (json['images'] as List).map((i) => ImageData.fromJson(i)).toList()
          : null,
      sections: json['sections'] != null
          ? (json['sections'] as List).map((s) => Section.fromJson(s)).toList()
          : null,
      like: json['like'],
      mainImage: ImageData.fromJson(json['main_image'] ?? {}),
    );
  }
}

class ImageData {
  final String? url;

  ImageData({required this.url});

  factory ImageData.fromJson(Map<String, dynamic> json) {
    return ImageData(url: json['url']);
  }
}

class Section {
  final String iconUrl;
  final String? title;
  final List<String>? items;

  Section({
    required this.iconUrl,
    this.title,
    this.items,
  });

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      iconUrl: json['icon_url'],
      title: json['title'],
      items: json['items'] != null ? List<String>.from(json['items']) : null,
    );
  }
}
