import 'package:get/get.dart';
import 'package:thrivve/core/controller/app_controller.dart';
import 'package:thrivve/core/helper/page_loading_dialog/page_loading_dialog.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/show_bottom_sheet_impl.dart';
import 'package:thrivve/core/helper/show_snack_bar/i_show_snackbar.dart';
import 'package:thrivve/core/injection_container.dart';
import 'package:thrivve/core/network_connection/network_controller.dart';
import 'package:thrivve/core/upload_image/file_upload_controller.dart';
import 'package:thrivve/features/dashboard/domain/use_cases/get_support_url_use_case.dart';
import 'package:thrivve/features/payment/binding/payment_binding.dart';
import 'package:thrivve/features/personalInfo/domain/use_cases/upload_image_use_case.dart';
import 'package:thrivve/features/stories_bar/presentation/manager/stories_controller.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(
      NetworkController(
        getIt<GetSupportUrlUseCase>(),
      ),
      permanent: true,
    );

    Get.put<IShowSnakBar>(
      ShowSnakBarImpl(),
      permanent: true,
    );
    Get.put<IShowBottomSheetHelper>(
      ShowBottomSheetHelperImpl(),
      permanent: true,
    );
    Get.put<IPageLoadingDialog>(
      PageLoadingDialog(),
      permanent: true,
    );

    Get.put(
      FileUploadControllerFactory(
        uploadImageUseCase: getIt(),
      ),
      permanent: true,
    );
    Get.put(
      StoriesController(),
      permanent: true,
    );
    Get.put(
      AppController(
        iPageLoadingDialog: Get.find(),
        logoutUseCase: getIt(),
        userSecureDataSource: getIt(),
      ),
      permanent: true,
    );

    PaymentBinding().dependencies();
    // _initPayment();
  }
}

class FileUploadControllerFactory {
  final UploadImageUseCase uploadImageUseCase;

  FileUploadControllerFactory({required this.uploadImageUseCase});

  FileUploadController create(String controllerId) {
    // Check if controller already exists with this tag
    if (Get.isRegistered<FileUploadController>(tag: controllerId)) {
      return Get.find<FileUploadController>(tag: controllerId);
    }

    // Create new controller instance
    final controller = FileUploadController(
      uploadImageUseCase: uploadImageUseCase,
      controllerId: controllerId,
    );

    // Register with tag
    Get.put(controller, tag: controllerId);

    return controller;
  }

  void dispose(String controllerId) {
    if (Get.isRegistered<FileUploadController>(tag: controllerId)) {
      Get.delete<FileUploadController>(tag: controllerId);
    }
  }
}
