import 'package:thrivve/features/dashboard/data/models/work_with_uber_all_data_model.dart';

class WorkWithUberAllDataModelTest extends WorkWithUberAllDataModel {
  WorkWithUberAllDataModelTest()
      : super(
          vehicleTypeId: 1,
          nationality: 'saudi',
          vehiclePricing:
              VehiclePricing(), // Assuming a default constructor exists
          averageWorkingTimeType: '+35',
          dateOfBirth: '2000-01-01',
          uberAccountEmail: '<EMAIL>',
          identityDocumentUrl: 'https://example.com/id.png',
          workVehicle: 'Car',
          campaignName: 'Test Campaign',
          hasUberAccount: true,
          captainMobile: '+*********',
          nationalId: '**********',
          cityId: 100,
          stepName: 'Initial Step',
          vehicle: Vehicle(), // Assuming a default constructor exists
          referralId: 'REF123',
          drivingLicenceUrl: 'https://example.com/license.png',
          workingTime: '3_6_months',
          customerOwnedCar: true,
          vehiclePricingId: 10,
          captainName: 'Test Captain',
          carRegistrationAttachmentUrl:
              'https://example.com/car_registration.png',
          city: City(
              nameEn: 'Riyadh',
              nameAr: 'الرياض'), // Assuming a default constructor exists
          age: 25,
          uberAccountPhone: '+*********',
          vehicleId: 20,
          customerId: 500,
        );
}
