import '../../domain/entities/rent_application_progress_entity.dart';
import '../models/rent_application_progress_model.dart';

extension RentApplicationProgressMapper on RentApplicationProgressModel {
  RentApplicationProgressEntity toEntity() {
    return RentApplicationProgressEntity(
      title: title,
      subTitle: subTitle,
      status: status,
      body: body,
    );
  }
}

extension RentApplicationProgressEntityMapper on RentApplicationProgressEntity {
  RentApplicationProgressModel toModel() {
    return RentApplicationProgressModel(
      title: title,
      subTitle: subTitle,
      status: status,
      body: body,
    );
  }
}
