import 'package:equatable/equatable.dart';

class ReturnOtp extends Equatable {
  final String? otp;
  final String? message;
  final bool? isSuccess;
  final String? title;
  final String? subTitle;
  final String? source;

  const ReturnOtp({
    this.otp,
    this.message,
    this.isSuccess,
    this.title,
    this.subTitle,
    this.source,
  });

  @override
  List<Object?> get props => [
        otp,
        message,
        isSuccess,
        title,
        subTitle,
        source,
      ];
}
