import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../onboarding/domain/entities/return_otp.dart';
import '../repositories/profile_repository.dart';

class SendOtpForChangePhoneUseCase
    implements UseCase<ReturnOtp?, SendOptParams> {
  ProfileRepository? profileRepository;

  SendOtpForChangePhoneUseCase({this.profileRepository});

  @override
  Future<Either<Failure, ReturnOtp?>?> call(SendOptParams params) async {
    return await profileRepository?.sendOtp(
      mobile: params.mobile,
    );
  }
}

class SendOptParams {
  String? mobile;

  SendOptParams({
    required this.mobile,
  });
}
