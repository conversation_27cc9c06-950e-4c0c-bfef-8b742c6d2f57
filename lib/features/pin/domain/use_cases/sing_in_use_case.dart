import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../onboarding/domain/repositories/auth_repository.dart';
import '../entities/thrivve_user.dart';

class SignInUseCase implements UseCase<ThrivveUser?, SignInParams> {
  AuthRepository? authRepository;

  SignInUseCase({this.authRepository});

  @override
  Future<Either<Failure, ThrivveUser?>?> call(SignInParams params) async {
    return await authRepository?.signIn(
        isNew: params.isNew,
        language: params.language,
        platform: params.platform,
        platformVersion: params.platformVersion,
        appVersion: params.appVersion,
        pushNotificationToken: params.pushNotificationToken);
  }
}

class SignInParams {
  String? pin;
  String? platform;
  String? platformVersion;
  String? appVersion;
  String? pushNotificationToken;
  String? language;
  bool? isNew;

  SignInParams({
    required this.language,
    required this.pin,
    required this.isNew,
    required this.platform,
    required this.platformVersion,
    required this.appVersion,
    required this.pushNotificationToken,
  });
}
