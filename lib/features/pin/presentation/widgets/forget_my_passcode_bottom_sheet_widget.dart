import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/features/pin/presentation/widgets/forget_my_passcode_3_widget.dart';
import 'package:thrivve/features/pin/presentation/widgets/forget_my_passcode_widget.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/helper.dart';
import '../manager/pin_bloc.dart';

class ForgetMyPasscodeBottomSheetWidget extends StatefulWidget {
  const ForgetMyPasscodeBottomSheetWidget({
    super.key,
  });

  @override
  State<ForgetMyPasscodeBottomSheetWidget> createState() =>
      _ForgetMyPasscodeBottomSheetWidgetState();
}

class _ForgetMyPasscodeBottomSheetWidgetState
    extends State<ForgetMyPasscodeBottomSheetWidget> {
  final controller = PageController(initialPage: 0);

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<PinBloc, PinState>(
          listenWhen: (previous, current) =>
              previous.verifyStatus != current.verifyStatus,
          listener: (context, state) {
            switch (state.verifyStatus) {
              case AppStatus.initial:
                break;
              case AppStatus.loading:
                // showLoaderDialog(context);
                break;
              case AppStatus.failure:
                // dismissLoaderDialog(context);
                errorSnackBar(context: context, message: state.errorMessage);
                break;
              case AppStatus.success:
                Get.offAllNamed(Routes.changePinScreen, arguments: {
                  'from': "forgetPin",
                });
                break;
              default:
            }
          },
        ),
        BlocListener<PinBloc, PinState>(
          listenWhen: (previous, current) =>
              previous.sendOtpStatus != current.sendOtpStatus,
          listener: (context, state) {
            switch (state.sendOtpStatus) {
              case AppStatus.initial:
                break;
              case AppStatus.loading:
                // showLoaderDialog(context);
                break;
              case AppStatus.failure:
                dismissLoaderDialog(context);
                errorSnackBar(context: context, message: state.errorMessage);
                break;
              case AppStatus.success:
                // dismissLoaderDialog(context);
                controller.animateToPage(
                  1,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeIn,
                );
                break;
            }
          },
        ),
      ],
      child: BlocBuilder<PinBloc, PinState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.viewInsetsOf(context)
                    .bottom, // Adjust for the keyboard
              ),
              child: Container(
                padding: EdgeInsets.all(16.h),
                decoration: BoxDecoration(
                  color: context.bottomsheetColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.0.r),
                    topRight: Radius.circular(20.0.r),
                  ),
                ),
                child: ExpandablePageView(
                  controller: controller,
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  children: [
                    ForgetMyPasscodeWidget(
                      onClickHelp: () {
                        context.read<PinBloc>().add(const GetSupportUrlEvent());
                      },
                      onPageChange: () {
                        context.read<PinBloc>().add(const SendOtpEvent());
                      },
                    ),
                    ForgetMyPasscode3Widget(
                      otpCode: state.otpCode,
                      mobileNumber: state.mobileNum,
                      onVerificationSubmit: (mobileNumber, otpCode) {
                        context.read<PinBloc>().add(
                              VerifyOtpEvent(
                                mobileNum: mobileNumber,
                                otpNum: otpCode,
                              ),
                            );
                      },
                      onBackClick: () {
                        controller.animateToPage(
                          0,
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeIn,
                        );
                      },
                      resendOTP: () {
                        context.read<PinBloc>().add(
                              ResentVerificationCodeEvent(
                                isLogin: true,
                                mobileNum: state.mobileNum,
                              ),
                            );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
