import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/features/dashboard/data/models/ClickDriveToOwnResponseModel.dart';
import 'package:thrivve/features/dashboard/data/models/change_language_model.dart';
import 'package:thrivve/features/dashboard/data/models/last_progress_model.dart';
import 'package:thrivve/features/dashboard/data/models/new_dashboard_products_model.dart';
import 'package:thrivve/features/dashboard/data/models/thrivve_info_model.dart';
import 'package:thrivve/features/dashboard/data/models/work_with_uber_all_data_model.dart';
import 'package:thrivve/features/personalInfo/data/models/personal_info_model.dart';

import '../../../../core/api/api_settings.dart';
import '../../../../core/error/exceptions.dart';
import '../../../invoices/domain/entities/invoice_filter.dart';
import '../../../invoices/domain/entities/invoice_filter_model.dart';
import '../../../invoices/domain/entities/invoice_v2.dart';
import '../../../transactions/data/models/transactions_filter_model.dart';
import '../../../transactions/domain/entities/transactions_filter.dart';
import '../../domain/entities/banners.dart';
import '../../domain/entities/insights.dart';
import '../../domain/entities/main_transaction.dart';
import '../../domain/entities/message_entity.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/entities/sync_notification.dart';
import '../../domain/entities/withdraw_balance.dart';
import '../../domain/entities/withdraw_config.dart';
import '../models/banners_model.dart';
import '../models/insights_model.dart';
import '../models/invoice_v2_model.dart';
import '../models/message_model.dart';
import '../models/payment_method_model.dart';
import '../models/products_model.dart';
import '../models/transactions_v2_list_model.dart';
import '../models/withdraw_config_model.dart';
import '../models/withdraw_model.dart';

abstract class DashboardRemoteDataSource {
  /// Call the {{thrivve}}/core/api/v1/me endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<PersonalInfoModel?>? getPersonalInfo();
  Future<ThrivveInfoModel?> getInfoThrivveData();

  //getHomePageMessage
  Future<MessageEntity?>? getHomePageMessage();

  /// Call the {{thrivve}}/finance/api/v1/me/balance endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<WithdrawBalance?>? getWithdrawBalance();

  /// Call the {{thrivve}}/core/api/v1/customer/banners endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<List<Banners>?>? getBanners();

  /// Call the {{thrivve}}/finance/api/v1/me/insights endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<Insights?>? getInsights();

  Future<WorkWithUberAllDataModel?>? getWorkWithUberAllData();

  /// Call the {{thrivve}}/finance/api/v1/transactions endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<MainTransaction?>? getTransactions({
    String? type,
    String? status,
    required int? page,
    required int? perPage,
    String? startDate,
    String? endDate,
    String? searchText,
  });

  /// Call the {{thrivve}}auth/api/v1/customer/language endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<ChangeLanguageModel?>? changeLanguage({String? language});

  /// Call the {{thrivve}}/auth/api/v1/me/delete endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<String?>? deleteUserAccount();

  /// Call the {{thrivve}}/finance/api/v1/payment_methods endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<List<PaymentMethod>?>? getPaymentMethods({String? paymentType});

  /// Call the {{thrivve}}/finance/api/v1/payment_methods endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<double?>? getWithdrawFees({required double? amount});

  /// Call the {{thrivve}}/finance/api/v1/requested_amount_configs endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<WithdrawConfig?>? getWithdrawConfig();

  /// Call the {{thrivve}}/finance/api/v1/products endpoint
  ///
  /// Throws [ServerException] for all error codes.
  Future<ProductsModel?>? getProducts(
      {bool? isPublic, int? page, int? perPage});

  Future<List<InvoiceV2>?>? getInvoices({
    String? status,
  });

  Future<List<InvoiceFilter>?>? getListOfFilter();

  // getListOfTransactionsFilter
  Future<List<TransactionsFilter>?>? getListOfTransactionsFilter();

  //getSyncInfo
  Future<SyncNotification?>? getSyncInfo();

  Future<String?>? getSupportUrl();

  //setSyncInfoSeen
  Future<String?>? setSyncInfoSeen();
  Future<LastProgressModel?>? getLastProgress();

  Future<NewDashboardProductsModel?> getNewDashboardProducts();
  Future<ClickDriveToOwnResponseModel?> clickDriveToOwn();
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;

  DashboardRemoteDataSourceImpl({required this.apiClient});
  @override
  Future<ThrivveInfoModel?> getInfoThrivveData() async {
    final response = await apiClient.request<ThrivveInfoModel>(
      endpoint: ApiSettings.thrivveInfo,
      fromJson: (json) => ThrivveInfoModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<PersonalInfoModel?>? getPersonalInfo() async {
    final response = await apiClient.request<PersonalInfoModel>(
      endpoint: ApiSettings.thrivveMyPersonalInfo,
      fromJson: (json) => PersonalInfoModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<MessageEntity?>? getHomePageMessage() async {
    final response = await apiClient.request<MessageEntity>(
      endpoint: ApiSettings.getThrivveHomePageMessage,
      fromJson: (json) {
        return MessageModel.fromJson(json ?? {});
      },
    );
    if (response.data?.title == null || response.data?.message == null) {
      return null;
    }
    return response.data;
  }

  @override
  Future<WithdrawBalance?>? getWithdrawBalance() async {
    final response = await apiClient.request<WithdrawBalance>(
      endpoint: ApiSettings.thrivveBalance,
      fromJson: (json) => WithdrawModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<List<Banners>?>? getBanners() async {
    final response = await apiClient.request<List<Banners>>(
      endpoint: ApiSettings.thrivveBanners,
      fromJson: (json) =>
          (json as List).map((x) => BannersModel.fromJson(x)).toList(),
    );
    return response.data;
  }

  @override
  Future<Insights?>? getInsights() async {
    final response = await apiClient.request<Insights>(
      endpoint: ApiSettings.thrivveInsights,
      fromJson: (json) => InsightsModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<MainTransaction?>? getTransactions({
    String? type,
    String? status,
    required int? page,
    required int? perPage,
    String? startDate,
    String? endDate,
    String? searchText,
  }) async {
    Map<String, dynamic> queryParameters = {
      "page": page,
      "per_page": perPage,
    };
    if (status != null) queryParameters["status"] = status;
    if (startDate != null) queryParameters["from_date"] = startDate;
    if (endDate != null) queryParameters["to_date"] = endDate;
    if (type != null) queryParameters["trans_type"] = type;
    if (searchText != null) queryParameters["search_txt"] = searchText;

    final response = await apiClient.request<MainTransaction>(
      endpoint: ApiSettings.thrivveTransactionsV4,
      queryParameters: queryParameters,
      fromJson: (json) {
        return TransactionV2ListModel.fromJson(json);
      },
    );
    return response.data;
  }

  @override
  Future<ChangeLanguageModel?>? changeLanguage({String? language}) async {
    final response = await apiClient.request<ChangeLanguageModel>(
      endpoint: ApiSettings.thrivveChangeLanguage,
      method: RequestType.post,
      data: {"language": language},
      headers: {"Accept-Language": language ?? "en"},
      fromJson: (json) => ChangeLanguageModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<String?>? deleteUserAccount() async {
    final response = await apiClient.request<Map<String, dynamic>>(
      endpoint: ApiSettings.thrivveDeleteAccount,
      method: RequestType.post,
      fromJson: (json) => json,
    );
    return response.data?["message"] as String?;
  }

  @override
  Future<List<PaymentMethod>?>? getPaymentMethods({String? paymentType}) async {
    final response = await apiClient.request<List<PaymentMethod>>(
      endpoint: ApiSettings.thrivvePaymentMethods,
      queryParameters: {"payment_type": paymentType},
      fromJson: (json) =>
          (json as List).map((x) => PaymentMethodModel.fromJson(x)).toList(),
    );
    return response.data;
  }

  @override
  Future<double?>? getWithdrawFees({required double? amount}) async {
    final response = await apiClient.request<Map<String, dynamic>>(
      endpoint: ApiSettings.thrivveWithdrawFees,
      queryParameters: {"amount": amount},
      fromJson: (json) => json,
    );
    return double.parse(response.data?["fees"].toString() ?? '0');
  }

  @override
  Future<WithdrawConfig?>? getWithdrawConfig() async {
    final response = await apiClient.request<WithdrawConfig>(
      endpoint: ApiSettings.thrivveWithdrawConfig,
      fromJson: (json) => WithdrawConfigModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<ProductsModel?>? getProducts(
      {bool? isPublic, int? page, int? perPage}) async {
    final response = await apiClient.request<ProductsModel>(
      endpoint: ApiSettings.thrivveGetProducts,
      queryParameters: {
        "is_public": isPublic ?? false,
        "page": page ?? 1,
        "per_page": perPage ?? 10
      },
      fromJson: (json) => ProductsModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<List<InvoiceV2>?>? getInvoices({String? status}) async {
    final response = await apiClient.request<List<InvoiceV2>>(
      endpoint: ApiSettings.thrivveGetInvoices,
      queryParameters: {
        "page": 1,
        "per_page": 100,
        if (status != null) "status": status,
      },
      fromJson: (json) => (json["items"] as List)
          .map((x) => InvoiceV2Model.fromJson(x))
          .toList(),
    );
    return response.data;
  }

  @override
  Future<List<InvoiceFilter>?>? getListOfFilter() async {
    final response = await apiClient.request<List<InvoiceFilter>>(
      endpoint: ApiSettings.thrivveGetListOfInvoicesFilter,
      fromJson: (json) =>
          (json as List).map((x) => InvoiceFilterModel.fromJson(x)).toList(),
    );
    return response.data;
  }

  @override
  Future<List<TransactionsFilter>?>? getListOfTransactionsFilter() async {
    final response = await apiClient.request<List<TransactionsFilter>>(
      endpoint: ApiSettings.thrivveTransactionsFilters,
      fromJson: (json) => (json as List)
          .map((x) => TransactionsFilterModel.fromJson(x))
          .toList(),
    );
    return response.data;
  }

  @override
  Future<SyncNotification?>? getSyncInfo() async {
    final response = await apiClient.request<SyncNotification?>(
      endpoint: ApiSettings.thrivveGetSyncInfo,
      fromJson: (json) => SyncNotification.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<String?>? setSyncInfoSeen() async {
    final response = await apiClient.request<Map<String, dynamic>>(
      endpoint: ApiSettings.thrivveGetSyncInfo,
      method: RequestType.post,
      fromJson: (json) => json,
    );
    return response.data?["message"] as String?;
  }

  @override
  Future<String?>? getSupportUrl() async {
    final response = await apiClient.request<Map<String, dynamic>>(
      endpoint: ApiSettings.thrivveSupport,
      fromJson: (json) => json,
    );
    return response.data?["url"] as String?;
  }

  @override
  Future<WorkWithUberAllDataModel?>? getWorkWithUberAllData() async {
    final response = await apiClient.request<WorkWithUberAllDataModel?>(
      endpoint: ApiSettings.workWithUberAllData,
      fromJson: (json) => WorkWithUberAllDataModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<LastProgressModel?>? getLastProgress() async {
    final response = await apiClient.request<LastProgressModel?>(
      endpoint: ApiSettings.lastProgress,
      fromJson: (json) => LastProgressModel.fromJson(json),
    );
    return response.data;
  }

  @override
  Future<NewDashboardProductsModel?> getNewDashboardProducts() async {
    final response = await apiClient.request<NewDashboardProductsModel>(
      endpoint: ApiSettings.getNewDashboardProducts,
      fromJson: (json) => NewDashboardProductsModel.fromJson(json),
    );
    return response.data!;
  }

  @override
  Future<ClickDriveToOwnResponseModel?> clickDriveToOwn() async {
    final response = await apiClient.request<ClickDriveToOwnResponseModel>(
      endpoint: ApiSettings.clickDriveToOwnComingSoon,
      method: RequestType.post,
      fromJson: (json) => ClickDriveToOwnResponseModel.fromJson(json),
    );
    return response.data;
  }
}
