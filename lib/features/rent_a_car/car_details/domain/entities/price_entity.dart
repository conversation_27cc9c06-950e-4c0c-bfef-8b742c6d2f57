import 'package:equatable/equatable.dart';

class RentCarPriceEntity extends Equatable {
  final double? discountPrice;
  final double? price;
  final String? currency;
  final double? totalPrice;
  final String? description;
  final String? period;

  const RentCarPriceEntity({
    this.discountPrice,
    this.price,
    this.currency,
    this.totalPrice,
    this.description,
    this.period,
  });

  @override
  List<Object?> get props => [
        discountPrice,
        price,
        currency,
        totalPrice,
        description,
      ];
}
