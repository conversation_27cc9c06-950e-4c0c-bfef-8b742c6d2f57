import 'dart:developer';

import 'package:sentry_flutter/sentry_flutter.dart';

import 'abstract/sentry_service_abstract.dart';

class SentryService implements ISentryService {
  static final SentryService _instance = SentryService._internal();

  // Public getter for the singleton instance
  static SentryService get instance => _instance;

  // Private constructor
  SentryService._internal();

  // Transaction Management
  ISentrySpan? _currentTransaction;
  final Map<String, ISentrySpan> _activeSpans = {};

  void _logUserInteraction(String action, {Map<String, dynamic>? data}) {
    // Log to console
    log(
      action,
      name: 'SentryService',
      error: data?.toString(),
    );
  }

  @override
  ISentrySpan startTransaction(String name, String operation) {
    _logUserInteraction('Start Transaction', data: {
      'name': name,
      'operation': operation,
      'type': 'transaction',
    });
    _currentTransaction = Sentry.startTransaction(name, operation);
    return _currentTransaction!;
  }

  @override
  ISentrySpan? startChildSpan(
    String name,
    String operation,
  ) {
    _logUserInteraction('Start Child Span', data: {
      'name': name,
      'operation': operation,
      'type': 'span',
    });
    if (_currentTransaction != null) {
      final childSpan = _currentTransaction!.startChild(
        operation,
        description: name,
        startTimestamp: DateTime.now(),
      );
      _activeSpans[name] = childSpan;
      return childSpan;
    } else {
      return null;
    }
  }

  @override
  void finishSpan(String name) {
    _logUserInteraction('Finish Span', data: {
      'name': name,
      'type': 'span',
    });
    final span = _activeSpans.remove(name);
    span?.finish(
      endTimestamp: DateTime.now(),
    );
  }

  @override
  void finishTransaction() {
    _logUserInteraction('Finish Transaction', data: {
      'type': 'transaction',
      'transaction_id': _currentTransaction?.context.traceId.toString(),
      'active_spans_count': _activeSpans.length,
    });
    _currentTransaction?.finish();
    _currentTransaction = null;
    _activeSpans.clear();
  }

  // Breadcrumb Management
  @override
  void addBreadcrumb({
    required String category,
    required String message,
    Map<String, dynamic>? data,
  }) {
    _logUserInteraction('Add Breadcrumb', data: {
      'category': category,
      'message': message,
      'type': 'breadcrumb',
      if (data != null) ...data,
    });
    Sentry.addBreadcrumb(
      Breadcrumb(
        message: message,
        category: category,
        data: data,
      ),
    );
  }

  // Error Tracking
  @override
  Future<SentryId> captureException(
    dynamic throwable, {
    dynamic stackTrace,
    Map<String, dynamic>? data,
  }) async {
    _logUserInteraction('Capture Exception', data: {
      'throwable': throwable.toString(),
      'stackTrace': stackTrace?.toString(),
      'type': 'exception',
      if (data != null) ...data,
    });
    return await Sentry.captureException(
      throwable,
      stackTrace: stackTrace,
      withScope: (scope) {
        if (data != null) {
          data.forEach((key, value) {
            scope.setContexts(key, value);
          });
        }
      },
    );
  }

  @override
  Future<SentryId> captureMessage(
    String message, {
    SentryLevel level = SentryLevel.info,
    Map<String, dynamic>? data,
  }) async {
    _logUserInteraction('Capture Message', data: {
      'message': message,
      'level': level.toString(),
      'type': 'message',
      if (data != null) ...data,
    });
    return await Sentry.captureMessage(
      message,
      level: level,
      withScope: (scope) {
        if (data != null) {
          data.forEach((key, value) {
            scope.setContexts(key, value);
          });
        }
      },
    );
  }

  // Scope Management
  @override
  void configureScope({
    Map<String, String>? tags,
    Map<String, dynamic>? extras,
    String? userId,
  }) {
    _logUserInteraction('Configure Scope', data: {
      'tags': tags,
      'extras': extras,
      'userId': userId,
      'type': 'scope',
    });
    Sentry.configureScope((scope) {
      if (tags != null) {
        tags.forEach((key, value) {
          scope.setTag(key, value);
        });
      }
      if (extras != null) {
        extras.forEach((key, value) {
          scope.setContexts(key, value);
        });
      }
      if (userId != null) {
        scope.setUser(SentryUser(id: userId));
      }
    });
  }

  // User Management
  @override
  void setUser(String? userId, {Map<String, dynamic>? userData}) {
    _logUserInteraction('Set User', data: {
      'userId': userId,
      'userData': userData,
      'type': 'user',
    });
    Sentry.configureScope((scope) {
      scope.setUser(
        SentryUser(
          id: userId,
          data: userData,
        ),
      );
    });
  }

  @override
  void clearUser() {
    _logUserInteraction('Clear User', data: {
      'type': 'user',
    });
    Sentry.configureScope((scope) {
      scope.setUser(null);
    });
  }

  @override
  Future<void> init(
    FlutterOptionsConfiguration optionsConfiguration, {
    AppRunner? appRunner,
  }) async {
    _logUserInteraction('Initialize Sentry', data: {
      'type': 'init',
    });
    await SentryFlutter.init(
      optionsConfiguration,
      appRunner: appRunner,
    );
  }
}
