import 'car_details.dart';

enum ListOfVehiclesViewFilter { carsOnly, bikesOnly }

extension ListOfVehiclesViewFilterX on ListOfVehiclesViewFilter {
  bool apply(CarDetails carDetails) {
    switch (this) {
      case ListOfVehiclesViewFilter.carsOnly:
        break;
      case ListOfVehiclesViewFilter.bikesOnly:
        break;
    }

    return true;
  }

  Iterable<CarDetails> applyAll(Iterable<CarDetails> carDetails) {
    return carDetails.where(apply);
  }
}
