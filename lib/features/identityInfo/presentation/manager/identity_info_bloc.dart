import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/user_cases/user_case.dart';
import '../../../../core/util/const.dart';
import '../../../../core/util/general_helper.dart';
import '../../../notifications/domain/use_cases/set_notification_seen_use_case.dart';
import '../../domain/entities/identity_info_result.dart';
import '../../domain/use_cases/get_identity_info_use_case.dart';
import '../../domain/use_cases/save_identity_info_use_case.dart';

part 'identity_info_event.dart';
part 'identity_info_state.dart';

class IdentityInfoBloc extends Bloc<IdentityInfoEvent, IdentityInfoState> {
  GetIdentityInfoUseCase getIdentityInfoUseCase;
  SaveIdentityInfoUseCase saveUserInfoUseCase;
  SetNotificationSeenUseCase setNotificationSeenUseCase;

  IdentityInfoBloc({
    required this.getIdentityInfoUseCase,
    required this.saveUserInfoUseCase,
    required this.setNotificationSeenUseCase,
  }) : super(const IdentityInfoState()) {
    on<SaveIdentityInfoEvent>(_onSaveIdentityInfoCalled);
    on<GetIdentityInfoEvent>(_onGetUserInfoCalled);
    on<DeleteFrontImageEvent>(_onDeleteFrontImageBtnClick);
    on<UploadFrontImageEvent>(_onUploadFrontImageEventCalled);
  }

  bool checkIfReadyToSubmit() {
    return !state.isFrontImageEmpty;
  }

  Future<void> _onUploadFrontImageEventCalled(
    UploadFrontImageEvent event,
    Emitter<IdentityInfoState> emit,
  ) async {
    emit(
      state.copyWith(
        frontImageUrl: () => event.fileUrl,
        uploadImageStatus: () => IdentityInfoStatus.success,
        isFrontImageEmpty: () => false,
        isDataSubmitReady: () => true,
      ),
    );
  }

  Future<void> _onSaveIdentityInfoCalled(
    SaveIdentityInfoEvent event,
    Emitter<IdentityInfoState> emit,
  ) async {
    if (state.frontImageUrl == null) {
      return;
    }

    emit(state.copyWith(saveDataStatus: () => IdentityInfoStatus.loading));

    final saveIdentityInfoUseCaseData =
        await saveUserInfoUseCase(SaveIdentityInfoParams(
      frontImage: state.frontImageUrl!,
    ));
    saveIdentityInfoUseCaseData?.fold(
        (failure) => emit(state.copyWith(
            saveDataStatus: () => IdentityInfoStatus.failure,
            errorMessage: () => mapFailureToMessage(failure))), (data) {
      emit(state.copyWith(
        message: () => data?.title,
        subtitle: () => data?.subtitle,
      ));
      if (event.notificationId == null) {
        emit(state.copyWith(saveDataStatus: () => IdentityInfoStatus.success));
      }
    });

    if (event.notificationId != null) {
      final setNotificationSeenUseCaseData =
          await setNotificationSeenUseCase(SetNotificationSeenParams(
        notificationIds: [event.notificationId!],
        isActionTaken: true,
      ));
      await setNotificationSeenUseCaseData?.fold(
          (failure) async => emit(
                state.copyWith(
                    errorMessage: () => mapFailureToMessage(failure)),
              ), (data) async {
        emit(state.copyWith(saveDataStatus: () => IdentityInfoStatus.success));
      });
    }
  }

  Future<void> _onDeleteFrontImageBtnClick(
    DeleteFrontImageEvent event,
    Emitter<IdentityInfoState> emit,
  ) async {
    emit(state.copyWith(
      isFrontImageEmpty: () => true,
      frontImageUrl: () => null,
    ));
    emit(state.copyWith(
      isDataSubmitReady: () => checkIfReadyToSubmit(),
    ));
  }

  Future<void> _onGetUserInfoCalled(
    GetIdentityInfoEvent event,
    Emitter<IdentityInfoState> emit,
  ) async {
    emit(state.copyWith(status: () => IdentityInfoStatus.loading));
    final getUserInfoUseCaseData = await getIdentityInfoUseCase(NoParams());
    getUserInfoUseCaseData?.fold(
        (failure) => emit(
              state.copyWith(
                status: () => IdentityInfoStatus.failure,
                errorMessage: () => mapFailureToMessage(failure),
              ),
            ), (data) {
      emit(
        state.copyWith(
          status: () => IdentityInfoStatus.success,
          identityInfoData: () => data,
          isSubmitRejected: () => data?.status == dataSubmitRejectedStatus,
          frontImageUrl: () => data?.identityFrontAttachment,
          isFrontImageEmpty: () => data?.identityFrontAttachment == null,
        ),
      );
    });
  }
}
