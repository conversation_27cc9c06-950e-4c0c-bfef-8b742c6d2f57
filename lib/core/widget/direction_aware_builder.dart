import 'package:flutter/material.dart';

class DirectionAwareBuilder extends StatelessWidget {
  const DirectionAwareBuilder({
    Key? key,
    required this.builder,
  }) : super(key: key);
  final Widget Function(BuildContext context, TextDirection textDirection)
      builder;
  @override
  Widget build(BuildContext context) {
    final dir = Directionality.of(context);
    return builder(context, dir);
  }
}
