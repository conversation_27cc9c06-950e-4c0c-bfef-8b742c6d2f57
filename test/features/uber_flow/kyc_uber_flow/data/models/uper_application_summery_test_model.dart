import 'package:thrivve/features/uber_flow/kyc_uber_flow/data/models/city_model.dart';
import 'package:thrivve/features/uber_flow/kyc_uber_flow/domain/entities/city_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/data/models/uper_request_summery_vehicle_model.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_application_summery_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_application_summery_vehicle_pricing_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_request_summery_vehicel_entity.dart';
import 'package:thrivve/features/uber_flow/review_uper_request.dart/domain/entities/uper_request_summery_vehicle_type_entity.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/data/models/vehicle_models.dart';
import 'package:thrivve/features/uber_flow/select_vehicles_uber_flow/domain/entities/vehicle_model_entity.dart';

class UperApplicationSummeryTestModel extends UperApplicationSummeryModel {
  UperApplicationSummeryTestModel()
      : super(
          hasUberAccount: false,
          vehicleType: null,
          customerId: 0,
          identityDocumentUrl: 'http://example.com/new_id.jpg',
          vehiclePricingId: 0,
          averageWorkingTimeType: '+35',
          vehicleModel: VehicleModel(
            id: 1,
            title: 'trtt',
            shouldRentCar: false,
          ),
          vehicle: UperApplicationSummeryVehicleModel(
            id: 1,
            manufacturer: 'Toyota',
            manufacturerModel: 'Corolla',
          ),
          uberAccountEmail: '<EMAIL>',
          workVehicle: UperApplicationSummeryVehicleModel(
            id: 1,
            manufacturer: 'Toyota',
            manufacturerModel: 'Corolla',
          ),
          dateOfBirth: DateTime(1985, 03, 15),
          drivingLicenceUrl: 'http://example.com/new_license.jpg',
          carRegistrationAttachmentUrl: 'https://example.com/car_reg.jpg',
          captainMobile: '**********',
          referralId: 'ABC123',
          customerOwnedCar: true,
          nationality: 'saudi',
          cityId: 1,
          vehicleId: 1,
          stepName: 'Step 1',
          captainName: 'John Doe',
          nationalId: '**********',
          campaignName: 'Test Campaign',
          vehiclePricing: null,
          workingTime: 'more_than_6_months',
          age: 30,
          uberAccountPhone: '**********',
          vehicleTypeId: 2,
          city: CityModel(id: "1", nameEn: 'Cairo', nameAr: 'القاهرة'),
        );
}

final dummyUperApplicationSummeryEntity = UperApplicationSummeryEntity(
  hasUberAccount: true,
  vehicleType: UperApplicationSummeryVehicleTypeEntity(
      id: null, textAr: '', textEn: ''), // Provide mock data for this class
  customerId: 12345,
  identityDocumentUrl: "",
  vehiclePricingId: 0,
  averageWorkingTimeType: "+35",
  vehicleModelEntity: VehicleModelEntity(),
  vehicle:
      UperApplicationSummeryVehicleEntity(), // Provide mock data for this class
  uberAccountEmail: "",
  workVehicle: null,
  dateOfBirth: DateTime(1985, 03, 15),
  drivingLicenceUrl: "",
  carRegistrationAttachmentUrl: "",
  captainMobile: "",
  referralId: null,
  customerOwnedCar: true,
  nationality: "",
  cityId: 1,
  vehicleId: 0,
  stepName: "",
  captainName: "",
  nationalId: "",
  campaignName: "",
  vehiclePricing: UperApplicationSummeryVehiclePricingEntity(
      subtitle: '', title: '', id: null), // Provide mock data
  workingTime: "",
  age: 0,
  uberAccountPhone: "",
  vehicleTypeId: 0,
  city: CityEntity(), // Provide mock data for this class
);
