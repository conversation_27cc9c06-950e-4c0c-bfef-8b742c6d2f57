// Example of Integration Test with Image Picker Mocking
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:thrivve/core/upload_image/custom_file_upload_widget.dart';
import 'package:thrivve/main_stg.dart' as app;

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  try {
    FlutterError.onError = (details) {
      log(details.exception.toString());
    };
    await initializeTest();
  } catch (ex) {
    log(ex.toString());
  }
}

// This function sets up a mock for the image picker method channel
Future<void> initializeTest() async {
  testWidgets('Complete user flow test', (WidgetTester tester) async {
    // Load app
    await app.main(testMode: true);

    // Instead of waiting for animations to complete with pumpAndSettle,
    // pump for a fixed duration to get past the splash screen
    await tester.pump(); // Initial frame
    await tester.pump(const Duration(seconds: 2));

    // Try to find the login button or any other widget that indicates
    // the splash screen has finished
    bool splashComplete = false;
    int attempts = 0;
    const maxAttempts = 15;

    while (!splashComplete && attempts < maxAttempts) {
      try {
        // Check for a widget that would be present after splash screen
        final loginButton = find.byKey(const ValueKey('loginButton'));
        final forgetMeButton = find.byKey(const ValueKey('forgetMeButton'));

        if (loginButton.evaluate().isNotEmpty ||
            forgetMeButton.evaluate().isNotEmpty) {
          splashComplete = true;
          break;
        }
      } catch (e) {
        // Ignore errors during this check
      }

      // Wait a second before trying again
      await tester.pump(const Duration(seconds: 1));
      attempts++;
      debugPrint('Waiting for splash screen to finish... Attempt: $attempts');
    }

    if (!splashComplete) {
      throw Exception(
          'Splash screen did not complete after $maxAttempts attempts');
    }

    // Now proceed with the rest of your test
    final forgetMe = find.byKey(const ValueKey('forgetMeButton'));
    if (forgetMe.evaluate().isNotEmpty) {
      await tester.tap(forgetMe);
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle(
          const Duration(seconds: 3)); // Add timeout to pumpAndSettle

      final yesButton = find.byKey(const ValueKey('yesButton'));
      await tester.tap(yesButton);
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle(const Duration(seconds: 3));
    }

    final loginButtonByKey = find.byKey(const ValueKey('loginButton'));
    debugPrint(
        'Login button by key found: ${loginButtonByKey.evaluate().isNotEmpty}');

    await tester.tap(loginButtonByKey);
    await tester.pumpAndSettle(const Duration(seconds: 1));

    final inputKey = find.byKey(const ValueKey('phoneNumberInput'));
    debugPrint('input key found: ${inputKey.evaluate().isNotEmpty}');

    // enter data
    await tester.enterText(inputKey, '599986191');
    await tester.pumpAndSettle(const Duration(seconds: 1));

    await tester.tap(find.byKey(const ValueKey('clickSubmitLogin')));
    await tester.pumpAndSettle(const Duration(seconds: 1));

    // We should now be on the PIN creation page
    expect(find.text("create_passcode".tr), findsOneWidget);

    // Create a new PIN
    await enterPin(tester, [1, 1, 1, 1, 1, 1]);
    await tester.pumpAndSettle(const Duration(seconds: 1));

    // Re-enter the same PIN
    await enterPin(tester, [1, 1, 1, 1, 1, 1]);
    await tester.pumpAndSettle(const Duration(seconds: 1));

    // Verify we're back on the dashboard
    await tester.pumpAndSettle(const Duration(seconds: 1));

    // click on go it update profile if found
    final gotItDashBoard = find.byKey(const ValueKey('gotItButton'));
    if (gotItDashBoard.evaluate().isNotEmpty) {
      debugPrint('input key found: ${inputKey.evaluate().isNotEmpty}');
      await tester.tap(gotItDashBoard);
      await tester.pumpAndSettle(const Duration(seconds: 1));
    }

    // click to deposit top up button
    final depositButton = find.byKey(const ValueKey('depositButton'));
    debugPrint(
        'depositButton key found: ${depositButton.evaluate().isNotEmpty}');
    await tester.tap(depositButton);
    await tester.pumpAndSettle(const Duration(seconds: 1));

    final instructionKey1 = find.text('insurance_instructions'.tr);
    final instructionKey2 = find.text('top_up_instructions'.tr);

    if (instructionKey1.evaluate().isNotEmpty ||
        instructionKey2.evaluate().isNotEmpty) {
      final understoodButtonInstruction =
          find.byKey(const ValueKey('underStoodInstruction'));
      if (understoodButtonInstruction.evaluate().isNotEmpty) {
        await tester.tap(understoodButtonInstruction);
        await tester.pumpAndSettle(const Duration(seconds: 1));
      }
    }
    // set amount in text field
    final inputTopUp = find.byKey(const ValueKey('inputTopUpAmount'));
    await tester.enterText(inputTopUp, '453');
    await tester.pumpAndSettle(const Duration(seconds: 1));

    // selection amount from list selection amount
    final amount100Button = find.byKey(const ValueKey('100'));
    if (amount100Button.evaluate().isNotEmpty) {
      expect(amount100Button, findsOneWidget);
      await tester.tap(amount100Button);
      await tester.pumpAndSettle(const Duration(seconds: 1));
    }

    // click next button in top up
    final nextButtonTopUp = find.byKey(const ValueKey('nextButton'));
    if (nextButtonTopUp.evaluate().isNotEmpty) {
      await tester.tap(nextButtonTopUp);
      await tester.pumpAndSettle(const Duration(seconds: 2));
    }

    // go to bank account and check
    final checkBoxAccountPage =
        find.byKey(const ValueKey('checkBoxAccountPage'));
    if (checkBoxAccountPage.evaluate().isNotEmpty) {
      await tester.tap(checkBoxAccountPage);
      await tester.pumpAndSettle(const Duration(seconds: 1));
    }

    final nextButtonBankAccount =
        find.byKey(const ValueKey('nextButtonBankAccount'));
    if (nextButtonBankAccount.evaluate().isNotEmpty) {
      await tester.tap(nextButtonBankAccount);
      await tester.pumpAndSettle(const Duration(seconds: 2));
    }

    // go to add receipt
    final selectPhoto = find.byType(CustomFileUploadWidget);
    if (selectPhoto.evaluate().isNotEmpty) {
      await tester.tap(selectPhoto);
      await tester.pumpAndSettle();
    }

    final imageSelectionOption =
        find.byKey(const ValueKey('imageSelectionOption'));
    if (imageSelectionOption.evaluate().isNotEmpty) {
      await tester.tap(imageSelectionOption);
      await tester.pumpAndSettle();

      // No need to interact with the gallery!
      // The mock image picker will automatically return our test image
      // when the app calls the image picker plugin

      debugPrint('Image should be selected automatically by the mock');
    }

    // Verify that an image has been selected (you may need to adjust this based on your UI)
    await tester.pumpAndSettle(const Duration(seconds: 2));

    final submitAddReceiptButton =
        find.byKey(const ValueKey('submitAddReceiptButton'));
    if (submitAddReceiptButton.evaluate().isNotEmpty) {
      await tester.tap(submitAddReceiptButton);
      await tester.pumpAndSettle();
    }

    final yesButton = find.byKey(const ValueKey('yesButton'));
    if (yesButton.evaluate().isNotEmpty) {
      await tester.tap(yesButton);
      await tester.pumpAndSettle();
    }

    // Stabilize before ending
    await tester.pumpAndSettle(const Duration(seconds: 2));

    // Suppress cleanup errors
    addTearDown(() async {
      try {
        await tester.pumpAndSettle();
      } catch (e) {
        print('Ignoring cleanup error: $e');
      }
    });
  });
}

Future<void> enterPin(WidgetTester tester, List<int> pinDigits) async {
  for (final digit in pinDigits) {
    // Find and tap the digit button
    await tester.tap(find.text("$digit".tr));
    await tester.pump(const Duration(milliseconds: 300));
  }
}
