import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/invoices/domain/entities/invoice_v2.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/general_helper.dart';
import '../../../../core/widget/invoice_web_page_viewer_bottom_sheet.dart';
import '../../../../generated/assets.dart';
import '../../../myProductDetails/presentation/widgets/pdf_viewer_bottom_sheet.dart';

class InvoiceV2Widget extends StatelessWidget {
  final InvoiceV2? invoice;
  final String currency;

  const InvoiceV2Widget({
    super.key,
    required this.invoice,
    this.currency = "SAR",
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 52.h,
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
        horizontalTitleGap: 10.w,
        leading: Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: context.appBackgroundColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Image.asset(
              Assets.thrivvePhotosInvoices,
              width: 20.w,
              height: 20.h,
            ),
          ),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: CustomTextWidget(
                      title: invoice?.title ?? "",
                      color: context.black,
                      fontWeight: FontWeight.w700,
                      size: 12,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 9.w, vertical: 3.h),
                    decoration: BoxDecoration(
                      color: invoice?.isPaid == true
                          ? context.badge2Color.withValues(alpha: 0.2)
                          : context.badge1Color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: CustomTextWidget(
                      title: invoice?.status ?? "",
                      color: context.black,
                      fontWeight: FontWeight.w600,
                      size: 10,
                    ),
                  ),
                ],
              ),
            ),
            CustomTextWidget(
              title: invoice?.amount ?? "",
              color: context.black,
              fontWeight: FontWeight.w600,
              size: 11,
            ),
          ],
        ),
        subtitle: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: CustomTextWidget(
                title: invoice?.description ?? "",
                color: context.lightBlack,
                fontWeight: FontWeight.w400,
                size: 11,
              ),
            ),
            InkWell(
              onTap: () {
                if (invoice?.url != null) {
                  getIt<IAnalyticsLogger>()
                      .logEvent(AnalyticsActions.viewInvoiceDetail);
                  openTheUriInWebPage(
                    downloadUrl: invoice?.downloadUrl?.split("?")[0],
                    url: invoice?.url,
                    context: context,
                  );
                }
              },
              child: Image.asset(
                Assets.thrivvePhotosDownload2,
                width: 24.w,
                height: 24.h,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showDocumentInPDF(
      BuildContext context, String? docPdfUrl, String? name) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return PdfViewerBottomSheet(
          isPDF: checkPDFFile(docPdfUrl ?? ""),
          url: docPdfUrl,
          title: "invoice".tr,
          showShareBtn: true,
        );
      },
    );
  }

  void openTheUriInWebPage(
      {String? url, required BuildContext context, String? downloadUrl}) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return Container(
          margin: EdgeInsets.only(top: 40.h),
          child: InvoiceWebPageViewerBottomSheet(
            url: url,
            downloadUrl: downloadUrl,
          ),
        );
      },
    );
  }
}
