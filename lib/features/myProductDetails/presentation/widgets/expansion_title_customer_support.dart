import 'package:flutter/material.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../core/util/date_util.dart';
import '../../../../generated/assets.dart';

class ExpansionTitleCustomerSupport extends StatelessWidget {
  final String? title;
  final DateTime? date;

  const ExpansionTitleCustomerSupport({
    this.date,
    this.title,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 4,
          child: Row(
            children: [
              Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(90.0),
                  color: context.imageBackgroundColor,
                ),
                child: Center(
                  child: Image.asset(
                    Assets.thrivvePhotosCustomerSupport,
                    height: 24,
                    width: 24,
                  ),
                ),
              ),
              const SizedBox(
                width: 8.0,
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title ?? "",
                      style: TextStyle(
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                          color: context?.black,
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      height: 4.0,
                    ),
                    Text(
                      DateUtil.getDateFormat(date) ?? "",
                      style: TextStyle(
                          fontSize: 12,
                          overflow: TextOverflow.ellipsis,
                          color: context?.statusBackground,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
