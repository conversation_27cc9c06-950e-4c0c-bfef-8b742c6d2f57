import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/app_client/app_client.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/flavor/flavor_config.dart';
import 'package:thrivve/features/onboarding/data/data_sources/auth_remote_data_source.dart';
import 'package:thrivve/features/onboarding/data/models/country_model.dart';
import 'package:thrivve/features/onboarding/data/models/otp_response_model.dart';
import 'package:thrivve/features/onboarding/data/models/return_otp_model.dart';
import 'package:thrivve/features/onboarding/domain/use_cases/send_otp_use_case.dart';
import 'package:thrivve/features/pin/data/models/thrivve_user_model.dart';

import '../../../../fixtures/fixture-reader.dart';
import 'auth_remote_data_source_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late MockApiClient mockApiClient;
  late AuthRemoteDataSource authRemoteDataSource;

  setUp(() {
    FlavorConfig(
      flavor: Flavor.dev,
    );
    mockApiClient = MockApiClient();
    authRemoteDataSource = AuthRemoteDataSourceImpl(
      apiClient: mockApiClient,
    );
  });

  group('signIn', () {
    final thrivveUser =
        ThrivveUserModel.fromJson(json.decode(fixture('thrivve_user.json')));
    const pin = 'pin';
    const language = 'language';
    const token = 'token';

    test('should successfully sign in', () async {
      when(mockApiClient.request<ThrivveUserModel>(
        endpoint: ApiSettings.thrivveLogin,
        method: RequestType.post,
        data: {
          'pin': pin,
          'language': language,
          'is_new': true,
          'platform': 'platform',
          'platform_version': 'platformVersion',
          'app_version': 'appVersion',
          'push_notification_token': 'pushNotificationToken'
        },
        headers: {
          'Authorization': token,
          'Accept-Language': language,
          'content-type': 'application/json'
        },
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
        fromJson: anyNamed('fromJson'),
      )).thenAnswer((_) async => ApiResponse(
            data: thrivveUser,
            statusCode: 200,
          ));

      final result = await authRemoteDataSource.signIn(
        language: language,
        platform: 'platform',
        platformVersion: 'platformVersion',
        appVersion: 'appVersion',
        pushNotificationToken: 'pushNotificationToken',
        isNew: true,
      );

      expect(result, equals(thrivveUser));
    });

    test('should throw ServerException when sign in fails', () {
      when(mockApiClient.request<ThrivveUserModel>(
        endpoint: ApiSettings.thrivveLogin,
        method: RequestType.post,
        fromJson: anyNamed('fromJson'),
        data: {
          'pin': pin,
          'language': language,
          'is_new': false,
          'platform': 'platform',
          'platform_version': 'platformVersion',
          'app_version': 'appVersion',
          'push_notification_token': 'pushNotificationToken'
        },
        headers: {
          'Authorization': token,
          'Accept-Language': language,
          'content-type': 'application/json'
        },
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenThrow(ServerException(msj: 'Failed to sign in'));

      expect(
        () => authRemoteDataSource.signIn(
          language: language,
          platform: 'platform',
          platformVersion: 'platformVersion',
          appVersion: 'appVersion',
          pushNotificationToken: 'pushNotificationToken',
          isNew: false,
        ),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('sendOtp', () {
    final returnOtp =
        ReturnOtpModel.fromJson(json.decode(fixture('return_otp_model.json')));
    const mobile = '1234567890';

    test('should successfully send OTP', () async {
      when(mockApiClient.request<ReturnOtpModel>(
        fromJson: anyNamed('fromJson'),
        endpoint: ApiSettings.thrivveSendOtp,
        method: RequestType.post,
        data: {'mobile': mobile},
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenAnswer((_) async => ApiResponse(
            data: returnOtp,
            statusCode: 200,
          ));

      final result = await authRemoteDataSource.sendOtp(params: SendOptParams(
        isLogin: true,
        mobile: mobile,
      ));
      expect(result, equals(returnOtp));
    });

    test('should throw ServerException when sending OTP fails', () {
      when(mockApiClient.request<ReturnOtpModel>(
        fromJson: anyNamed('fromJson'),
        endpoint: ApiSettings.thrivveSendOtp,
        method: RequestType.post,
        data: {'mobile': mobile},
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenThrow(ServerException(msj: 'Failed to send OTP'));

      expect(
        () => authRemoteDataSource.sendOtp(params: SendOptParams(
          isLogin: true,
          mobile: mobile,
        )),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('verifyOtp', () {
    final otpResponse =
        OtpResponseModel.fromJson(json.decode(fixture('verify_model.json')));
    const mobile = '1234567890';
    const otpCode = '1234';

    test('should successfully verify OTP', () async {
      when(mockApiClient.request<OtpResponseModel>(
        endpoint: ApiSettings.thrivveVerify,
        fromJson: anyNamed('fromJson'),
        method: RequestType.post,
        data: {
          'mobile': mobile,
          'otp_code': otpCode,
        },
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenAnswer((_) async => ApiResponse(
            data: otpResponse,
            statusCode: 200,
          ));

      final result = await authRemoteDataSource.verifyOtp(
        mobile: mobile,
        otpCode: otpCode,
      );
      expect(result, equals(otpResponse));
    });

    test('should throw ServerException when verifying OTP fails', () {
      when(mockApiClient.request<OtpResponseModel>(
        endpoint: ApiSettings.thrivveVerify,
        fromJson: anyNamed('fromJson'),
        method: RequestType.post,
        data: {
          'mobile': mobile,
          'otp_code': otpCode,
        },
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenThrow(ServerException(msj: 'Failed to verify OTP'));

      expect(
        () => authRemoteDataSource.verifyOtp(
          mobile: mobile,
          otpCode: otpCode,
        ),
        throwsA(isA<ServerException>()),
      );
    });
  });

  group('getCountries', () {
    final countries = (json.decode(fixture('country_list.json')) as List)
        .map((x) => CountryModel.fromJson(x))
        .toList();

    test('should successfully get countries', () async {
      when(mockApiClient.request<List<CountryModel>>(
        endpoint: ApiSettings.getCountries,
        fromJson: anyNamed('fromJson'),
        method: RequestType.get,
        data: null,
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenAnswer((_) async => ApiResponse(
            data: countries,
            statusCode: 200,
          ));

      final result = await authRemoteDataSource.getCountries();
      expect(result, equals(countries));
    });

    test('should throw ServerException when getting countries fails', () {
      when(mockApiClient.request<List<CountryModel>>(
        endpoint: ApiSettings.getCountries,
        fromJson: anyNamed('fromJson'),
        method: RequestType.get,
        data: null,
        headers: {'Authorization': null},
        queryParameters: null,
        cancelToken: null,
        onSendProgress: null,
      )).thenThrow(ServerException(msj: 'Failed to get countries'));

      expect(
        () => authRemoteDataSource.getCountries(),
        throwsA(isA<ServerException>()),
      );
    });
  });
}
