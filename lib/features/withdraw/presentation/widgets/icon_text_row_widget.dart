import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';

import '../../../../core/util/general_helper.dart';
import '../../../../generated/assets.dart';
import '../../../myProductDetails/presentation/widgets/pdf_viewer_bottom_sheet.dart';

class IconTextRowWidget extends StatelessWidget {
  //bool isFirst, bool isLast, String icon, String text
  final bool isFirst;
  final bool isLast;
  final String? icon;
  final String? text;
  final String? url;
  final bool? isClickable;

  const IconTextRowWidget({
    super.key,
    required this.isFirst,
    required this.isLast,
    required this.icon,
    required this.text,
    this.url,
    this.isClickable,
  });

  @override
  Widget build(BuildContext context) {
    final iconData = getIconFromAssets(icon);
    final isSvg = iconData.split('.').last.toLowerCase() == 'svg';
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: isFirst ? 30.h * 2 : 30.h,
              width: 1.w,
              decoration: BoxDecoration(
                color: context.borderLeaseColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Icon(Icons.circle, size: 10.sp, color: context.borderLeaseColor),
            Container(
              height: isLast ? 30.h * 2 : 30.h,
              width: 1.w,
              decoration: BoxDecoration(
                color: context.borderLeaseColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ],
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Container(
            margin: EdgeInsets.only(
                top: isFirst ? 30.h : 0.h, bottom: isLast ? 30.h : 0.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                isSvg
                    ? SvgPicture.asset(
                        iconData,
                        width: 24.w,
                        height: 24.h,
                        fit: BoxFit.cover,
                        color: context.black,
                      )
                    : Image.asset(
                        iconData,
                        width: 24.w,
                        height: 24.h,
                        fit: BoxFit.cover,
                        color: context.black,
                      ),
                SizedBox(width: 16.w),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (isClickable != null && isClickable!) {
                        showDocumentInPDF(context, url, "receipt".tr);
                      }
                    },
                    child: CustomFormattedText(
                      text: text ?? "",
                      textAlign: WrapAlignment.start,
                      strongStyle: TextStyle(
                        fontSize: 12,
                        color: context?.lightBlack,
                        fontFamily: Get.locale?.languageCode == 'en'
                            ? "NotoSans"
                            : "NotoSansArabic",
                        height: 1.5,
                        fontWeight: FontWeight.w700,
                      ),
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: Get.locale?.languageCode == 'en'
                            ? "NotoSans"
                            : "NotoSansArabic",
                        height: 1.5,
                        color: context?.lightBlack,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  void showDocumentInPDF(
      BuildContext context, String? docPdfUrl, String? name) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return PdfViewerBottomSheet(
          isPDF: checkPDFFile(docPdfUrl ?? ""),
          url: docPdfUrl,
          title: name ?? "",
          showShareBtn: false,
        );
      },
    );
  }

  String getIconFromAssets(String? icon) {
    switch (icon) {
      case "wallet":
        return Assets.thrivvePhotosWallet;
      case "clock":
        return Assets.assetsThrivvePhotosClock;
      case "bank":
        return Assets.thrivvePhotosBank;
      case "receipt":
        return Assets.assetsThrivvePhotosReceipt;
      default:
        return Assets.thrivvePhotosCashFromUberIcon;
    }
  }
}
