import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/util/phone_dilar.dart';
import 'package:thrivve/core/widget/app_loading_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebPageViewerBottomSheet extends StatefulWidget {
  final String? url;

  const WebPageViewerBottomSheet({super.key, this.url});

  @override
  State<WebPageViewerBottomSheet> createState() =>
      _WebPageViewerBottomSheetState();
}

class _WebPageViewerBottomSheetState extends State<WebPageViewerBottomSheet> {
  String? title;
  bool isLoading = true;

  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory(() => EagerGestureRecognizer())
  };

  final UniqueKey _key = UniqueKey();
  late WebViewController _controller;

  @override
  void initState() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..addJavaScriptChannel(
        "Flutter",
        onMessageReceived: (message) {
          String pageBody = message.message;
          if (kDebugMode) {
            print('page body: $pageBody');
          }
        },
      )
      ..addJavaScriptChannel(
        "ReactNativeWebView",
        onMessageReceived: (message) {
          _handleJavaScriptMessage(message.message);
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (progress == 100) {
              if (mounted) {
                setState(() {
                  isLoading = false;
                });
              }
            }
            if (kDebugMode) {
              print('WebView is loading (progress : $progress%)');
            }
          },
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                isLoading = true;
              });
            }
            if (kDebugMode) {
              print('Page started loading: $url');
            }
          },
          onPageFinished: (String url) async {
            String script = 'window.document.title';
            String? title = (await _controller
                .runJavaScriptReturningResult(script)) as String?;
            String? result = title?.replaceAll(RegExp('["]'), '');
            if (kDebugMode) {
              print('Page finished loading: $result');
            }
            if (mounted) {
              setState(() {
                this.title = result;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (kDebugMode) {
              print('Web resource error: ${error.description}');
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            // Check if the link is a deep link
            if (request.url.startsWith("thrivve://") ||
                request.url.startsWith("market://")) {
              print('Blocking deep link: ${request.url}');
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url ?? ""));
    super.initState();
  }

  void _handleJavaScriptMessage(String message) {
    if (kDebugMode) {
      print('JavaScript message received: $message');
    }

    var action =
        message.split('/')[0].isEmpty ? message : message.split('/')[0];

    switch (action) {
      case 'closeWebView':
      case 'complete':
        Navigator.pop(context, true);
        break;
      case 'page':
        var page = message.split('/')[1];
        Navigator.pop(context, page);
        break;
      case 'externalLink':
        var url = message.replaceAll('externalLink/', '');
        _handleExternalLink(url);
        break;
      case 'new_register':
        Navigator.pop(context);
        _showLoginOrRegister();
        break;
      case 'call_dial':
        var phone = message.split('/')[1];
        _dialPhone(phone);
        break;
      default:
        if (kDebugMode) {
          print('Unhandled JavaScript message: $message');
        }
        break;
    }
  }

  void _dialPhone(String phone) {
    PhoneDialer.dial(phone);
  }

  Future<void> _handleExternalLink(String url) async {
    // Implement external link handling here
    final link = url.replaceAll('externalLink/', '');
    if (link.isNotEmpty && (await canLaunchUrl(Uri.parse(link)))) {
      await launchUrl(Uri.parse(link), mode: LaunchMode.externalApplication);
    }
  }

  void _showLoginOrRegister() {
    // Implement login/register logic here
    if (kDebugMode) {
      print('Show login or register');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: context.containerColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0.r),
          topRight: Radius.circular(16.0.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: Icon(
                  Icons.refresh_outlined,
                  size: 24,
                  color: context.lightBlack,
                ),
                onPressed: () {
                  _controller.reload();
                },
              ),
              Expanded(
                child: Text(
                  title ?? "",
                  style: TextStyle(
                    color: context.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.close,
                  size: 24,
                  color: context.lightBlack,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: context.cardHomeContainer,
          ),
          Expanded(
            child: Stack(
              children: [
                WebViewWidget(
                  key: _key,
                  gestureRecognizers: gestureRecognizers,
                  controller: _controller,
                ),
                if (isLoading) const AppLoadingWidget()
              ],
            ),
          )
        ],
      ),
    );
  }
}
