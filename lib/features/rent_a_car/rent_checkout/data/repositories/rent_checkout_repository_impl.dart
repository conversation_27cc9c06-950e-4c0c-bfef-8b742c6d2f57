import 'package:dartz/dartz.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/core/network/network_info.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/data_sources/rent_checkout_remote_data_source.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/calendar_response_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/checkout_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/checkout_paymob_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/kyc_input_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/data/mappers/kyc_response_mapper.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/calendar_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/checkout_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/checkout_paymob_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_input_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/entities/kyc_response_entity.dart';
import 'package:thrivve/features/rent_a_car/rent_checkout/domain/repositories/rent_checkout_repository.dart';

class RentCheckoutRepositoryImpl implements RentCheckoutRepository {
  final RentCheckoutRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  RentCheckoutRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, CalendarResponseEntity?>> getPickupCalendar() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getPickupCalendar();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, CheckoutResponseEntity?>> getCheckoutRentACar() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getCheckoutRentACar();
        final entity = result?.toEntity();
        return Right(entity);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, KYCResponseEntity?>> submitKYC(
    KYCInputEntity kycInput,
  ) async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.submitKYC(
            kycInput.toModel().toJson(), kycInput.type);
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, CheckoutResponsePaymobEntity?>>
      submitCheckout() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.submitCheckout();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
