import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/features/uber_flow/uber_global_widgets/back_icon.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/app_status.dart';
import '../../domain/entities/faq.dart';
import '../manager/faq_bloc.dart';
import '../manager/faq_event.dart';
import '../manager/faq_state.dart';
import '../widgets/faq_detail_dialog.dart';
import '../widgets/faq_search_field.dart';

class FaqScreen extends StatefulWidget {
  const FaqScreen({super.key});

  @override
  State<FaqScreen> createState() => _FaqScreenState();
}

class _FaqScreenState extends State<FaqScreen> {
  // is expanded
  bool isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create: (context) => getIt<FaqBloc>()..add(FetchFaqs()),
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              surfaceTintColor: Colors.transparent,

              expandedHeight: 160.h,
              floating: true,
              pinned: true,
              snap: true,
              elevation: 0,
              leading: Center(
                child: BackIcon(
                  onClickBack: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
              title: isExpanded
                  ? null
                  : Text(
                      'faqs'.tr,
                      style: TextStyle(
                        color: context.black,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
              // title is centered
              centerTitle: true,
              flexibleSpace: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  // check if the app bar is expanded

                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    // This will execute after the current frame is built, avoiding the issue.
                    if (constraints.maxHeight > 100.h) {
                      setState(() {
                        isExpanded = true;
                      });
                    } else {
                      setState(() {
                        isExpanded = false;
                      });
                    }
                  });

                  return FlexibleSpaceBar(
                    background: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'faqs'.tr,
                            style: TextStyle(
                              color: context?.black,
                              fontSize: 28.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          BlocBuilder<FaqBloc, FaqState>(
                            builder: (context, state) {
                              return SearchFieldWithClear(
                                onChange: (searchText) {
                                  getIt<IAnalyticsLogger>().logEvent(
                                      AnalyticsActions
                                          .searchFAQs); //Need an enhancement.
                                  context
                                      .read<FaqBloc>()
                                      .add(FetchFaqs(searchText: searchText));
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0.h),
                child: BlocBuilder<FaqBloc, FaqState>(
                  builder: (context, state) {
                    if (state.status == AppStatus.loading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state.status == AppStatus.success) {
                      // check if faqs is empty
                      if (state.faqs!.isEmpty) {
                        return Center(child: Text('no_faqs_available'.tr));
                      }
                      return _buildFaqContent(state.faqs!);
                    } else if (state.status == AppStatus.failure) {
                      return Center(
                          child: Text(
                              state.errorMessage ?? 'Error loading FAQ data.'));
                    } else {
                      return Center(child: Text('no_faqs_available'.tr));
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget to display the FAQ list content
  Widget _buildFaqContent(List<Faq> faqs) {
    // Group FAQs by category
    final groupedFaqs = _groupFaqsByCategory(faqs);

    return Builder(builder: (context) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // SizedBox(height: 36.h),
          ...groupedFaqs.entries.map((entry) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.key,
                  style: TextStyle(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                    color: context.lightBlack,
                  ),
                ),
                SizedBox(height: 4.h),
                Divider(
                  color: context.black!.withOpacity(0.20),
                  thickness: 0.4.h,
                ),
                ...entry.value.map((faq) {
                  return SizedBox(
                    height: 52.h,
                    child: ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Text(
                        faq.question,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w700,
                          color: context?.black,
                        ),
                      ),
                      trailing: Icon(Icons.arrow_forward_ios,
                          size: 16.sp, color: context?.lightBlack),
                      onTap: () {
                        getIt<IAnalyticsLogger>().logEvent(
                            AnalyticsActions.viewFAQDetails,
                            parameters: {
                              AnalyticsActions.questionTitle: faq.question
                            });
                        var bloc = context.read<FaqBloc>()
                          ..add(FetchFaqById(faq.id));
                        showModalBottomSheet(
                            context: context,
                            backgroundColor: Colors.transparent,
                            isScrollControlled: true,
                            builder: (_) {
                              return BlocProvider.value(
                                value: bloc,
                                child: BlocBuilder<FaqBloc, FaqState>(
                                  builder: (context, state) {
                                    switch (state.getFaqsStatus) {
                                      case AppStatus.loading:
                                        return const Center(
                                          child: CircularProgressIndicator(),
                                        );
                                      case AppStatus.success:
                                        return FaqDetailDialog(
                                            faq: state.faqById!);
                                      case AppStatus.failure:
                                        return Center(
                                          child: Text(
                                            state.errorMessage ??
                                                'Error loading FAQ data.',
                                          ),
                                        );
                                      default:
                                        return const Center(
                                          child: Text('No FAQs available.'),
                                        );
                                    }
                                  },
                                ),
                              );
                            });
                      },
                    ),
                  );
                }),
                SizedBox(height: 36.h),
              ],
            );
          }),
        ],
      );
    });
  }

  // Group FAQs by their category
  Map<String, List<Faq>> _groupFaqsByCategory(List<Faq> faqs) {
    Map<String, List<Faq>> groupedFaqs = {};

    for (var faq in faqs) {
      if (faq.category == null) {
        continue;
      }
      String category = faq.category!.text; // Assuming English is used
      if (!groupedFaqs.containsKey(category)) {
        groupedFaqs[category] = [];
      }
      groupedFaqs[category]!.add(faq);
    }

    return groupedFaqs;
  }
}
