import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class IdentityInfoWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;

  const IdentityInfoWidget(
      {super.key, required this.subtitle, required this.title});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        decoration: BoxDecoration(
          color: context.containerColor,
          borderRadius: BorderRadius.circular(12),
        ),
        height: 346,
        margin: const EdgeInsets.only(bottom: 24, left: 18, right: 18),
        child: SizedBox.expand(
          child: Material(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Image.asset(
                          Assets.thrivvePhotosThrivveBlackLogo,
                          height: 24,
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Ink<PERSON>ell(
                          key: const Key("iconBtn"),
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: context?.iconAndBtnBackground,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.grey,
                              size: 20,
                            ),
                          ),
                          onTap: () {
                            Get.back();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  width: 24,
                ),
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: context.greenBackgroundColor,
                  ),
                  width: 128,
                  height: 128,
                  child: Icon(
                    Icons.check,
                    size: 44,
                    color: context.whiteColor,
                  ),
                ),
                const SizedBox(
                  height: 24,
                ),
                Text(
                  "successfully_submitted".tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                Text(
                  title ?? "",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: context.statusBackground,
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                Container(
                  margin: const EdgeInsets.only(right: 16.0, left: 16.0),
                  padding: const EdgeInsets.only(
                      right: 16.0, left: 16.0, top: 8.0, bottom: 8.0),
                  decoration: BoxDecoration(
                      color: context.appPrimaryColor!.withOpacity(0.10),
                      borderRadius: const BorderRadius.all(Radius.circular(8))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        Assets.thrivvePhotosTime,
                        width: 20,
                        height: 30,
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Text(
                        subtitle ?? "",
                        style: TextStyle(
                            color: context.appPrimaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w400),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
