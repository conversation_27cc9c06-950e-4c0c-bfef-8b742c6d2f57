import 'dart:developer';

import 'package:thrivve/core/flavor/flavor_config.dart';

abstract class IAnalyticsLogger {
  List<Flavor> get supportedEnvironments;
  Future<void> sendUserId(String id);
  Future<void> logEvent(String name, {Map<String, Object>? parameters});
  Future<void> logProperty(String name, String value);
  Future<void> logScreenView({
    String? screenClass,
    String? screenName,
  });
}

class MockAnalyticsLogger implements IAnalyticsLogger {
  @override
  Future<void> logEvent(String name, {Map<String, dynamic>? parameters}) async {
    log('==>LogEvent: $name,parameters: $parameters');
  }

  @override
  Future<void> logProperty(String name, String value) async {
    log('==>LogProperty: $name, value: $value');
  }

  @override
  List<Flavor> get supportedEnvironments => [
        Flavor.prod,
        Flavor.stg,
        Flavor.dev,
      ];

  @override
  Future<void> sendUserId(String userId) async {}

  @override
  Future<void> logScreenView({String? screenClass, String? screenName}) async {
    log('==>screenClass: $screenClass, screenName: $screenName');
  }
}
