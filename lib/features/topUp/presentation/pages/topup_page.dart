import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/analytics/analytics_logger.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/i_show_bottom_sheet.dart';
import 'package:thrivve/core/helper/show_bottom_sheet/show_bottom_sheet_input.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/util/app_status.dart';
import 'package:thrivve/core/util/payment_utils.dart';
import 'package:thrivve/core/util/string_utils.dart';
import 'package:thrivve/core/widget/button.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/core/widget/styled_decimal_text_widget.dart';
import 'package:thrivve/features/topUp/domain/entities/checkout_input_entity.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_method_entity.dart';
import 'package:thrivve/features/topUp/domain/enum/card_type_enum.dart';
import 'package:thrivve/features/topUp/presentation/widgets/payment_card_widget.dart';
import 'package:thrivve/features/topUp/presentation/widgets/payment_method_sheet.dart';

import '../../../../core/injection_container.dart';
import '../../../../core/util/analytics_actions.dart';
import '../../../../core/util/helper.dart';
import '../../../../core/widget/styleableTextField/stylable_text_field_controller.dart';
import '../../../../core/widget/styleableTextField/text_part_style_definition.dart';
import '../../../../core/widget/styleableTextField/text_part_style_definitions.dart';
import '../../../../generated/assets.dart';
import '../../../withdraw/presentation/widgets/amount_selection.dart';
import '../manager/top_up_bloc.dart';

enum PaymentTypeTopUp {
  bank,
  online,
}

class TopUpPage extends StatefulWidget {
  const TopUpPage({super.key});

  @override
  State<TopUpPage> createState() => _TopUpPageState();
}

class _TopUpPageState extends State<TopUpPage> {
  final FocusNode _focusNode = FocusNode();
  late StyleableTextFieldController textEditingController;
  @override
  void initState() {
    super.initState();
    textEditingController = StyleableTextFieldController(
        styles: TextPartStyleDefinitions(definitionList: [
      TextPartStyleDefinition(
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.w600,
          ),
          pattern: r'^(\d+)'),
      TextPartStyleDefinition(
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
          pattern: r'.(\d+)$'),
    ]));
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return getIt<TopUpBloc>()
          ..add(GetThrivveBankDetailsEvent())
          ..add(FetchPaymentMethodsEvent())
          ..add(FetchSavedCardsEvent());
      },
      child: MultiBlocListener(
        listeners: _listeners(),
        child: BlocBuilder<TopUpBloc, TopUpState>(
          builder: (context, state) {
            return GestureDetector(
              onTap: () {
                if (_focusNode.hasFocus) {
                  _focusNode
                      .unfocus(); // unfocus only if the TextField is focused
                }
              },
              child: Scaffold(
                appBar: _appBar(state),
                body: SafeArea(
                  child: Column(
                    children: [
                      _body(context, state),
                      SizedBox(height: 20.h),
                      _bottomWidget(state, context),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _bottomWidget(TopUpState state, BuildContext context) {
    final topUp = state.topUp;
    return Container(
      color: context.backgroundColor,
      child: Column(
        children: [
          // Padding(
          //   padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w, top: 8.0.h),
          //   child: NotesWithBoldWidget(
          //     notes: ["currently_only_bank_transfer_is_supported".tr],
          //   ),
          // ),
          Divider(
            color: Colors.grey,
            thickness: 0.4.h,
            height: 0,
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 16.0.w,
              right: 16.0.w,
              bottom: 24.h.h,
              top: 12.0.h,
            ),
            child: Button(
              key: ValueKey('nextButton'),
              // enable:true,
              enable: ((topUp?.isInsurancePayment ?? false)
                      ? true
                      : state.confirmBtnStatus ?? false) &&
                  (state.listPayment ?? []).isNotEmpty,
              text: "next".tr,
              onTab: () async {
                if (_focusNode.hasFocus) {
                  _focusNode
                      .unfocus(); // unfocus only if the TextField is focused
                }
                getIt<IAnalyticsLogger>()
                    .logEvent(AnalyticsActions.topUpAmountEntered, parameters: {
                  AnalyticsActions.enteredTypeParams: checkTheAmountEntered(
                      textEditingController.text, topUp?.suggestedAmounts ?? [])
                });
                if (state.selectedPaymentMethod?.type == CardTypeEnum.Bank) {
                  final isSeenInstruction =
                      await getIt<UserSecureDataSource>().isSeenInstruction();
                  final isSeenInstructionApplication =
                      await getIt<UserSecureDataSource>()
                          .isSeenInstructionApplication();
                  if ((state.topUp?.isInsurancePayment ?? false)
                      ? (isSeenInstructionApplication ?? false).inverted
                      : (isSeenInstruction ?? false).inverted) {
                    Get.toNamed(AppRoutes.topUpInstructionsPage, arguments: {
                      "topUp": state.topUp,
                      "topUpBloc": context.read<TopUpBloc>(),
                    })?.then(
                      (value) {
                        Get.back(result: value);
                      },
                    );
                  } else {
                    Get.toNamed(
                      AppRoutes.topUpBankAccountPage,
                      arguments: {'topUpBloc': context.read<TopUpBloc>()},
                    )?.then(
                      (value) {
                        Get.back(result: value);
                      },
                    );
                  }
                } else {
                  final input = CheckoutInputEntity(
                    paymentGateway: 'Paymob',
                    amount: topUp?.onlinePaymentItemId == null
                        ? (state.topUpAmount ?? 0).toString()
                        : null,
                    integrationPaymentId: state.selectedPaymentMethod?.id,
                    itemId: topUp?.onlinePaymentItemId,
                  );
                  context.read<TopUpBloc>().add(
                        CheckOutPaymentEvent(
                          checkoutInputEntity: input,
                        ),
                      );
                }
              },
              height: 36.h,
              fontWeight: FontWeight.w600,
            ),
          )
        ],
      ),
    );
  }

  AppBar _appBar(TopUpState state) {
    return AppBar(
      leadingWidth: 66.w,
      leading: Center(
        child: Container(
          height: 40.h,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.appBackgroundColor,
          ),
          child: IconButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: Icon(
                Icons.arrow_back_rounded,
                color: context.black,
                size: 20.w,
              )),
        ),
      ),
      // actions: [
      //   SizedBox(
      //     width: 16.w,
      //   ),
      //   Container(
      //     height: 40.h,
      //     width: 40.w,
      //     decoration: BoxDecoration(
      //       shape: BoxShape.circle,
      //       color: context.appBackgroundColor,
      //     ),
      //     child: IconButton(
      //         onPressed: () {
      //           getIt<IAnalyticsLogger>()
      //               .logEvent(AnalyticsActions.topUpQuestionMarkClick);
      //           Get.toNamed(AppRoutes.topUpInstructionsPage, arguments: {
      //             "from_app_bar": true,
      //             "topUp": state.topUp,
      //           });
      //         },
      //         icon: Icon(
      //           Icons.question_mark_rounded,
      //           color: context.black,
      //           size: 20.w,
      //         )),
      //   ),
      //   SizedBox(
      //     width: 16.w,
      //   ),
      // ],
    );
  }

  Widget titlePayment(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CustomTextWidget(
          title: 'payment_method_onboarding_title'.tr.orDefault,
          fontWeight: FontWeight.w600,
          size: 11,
          textAlign: TextAlign.start,
          color: context.black.withValues(alpha: 0.6),
        ),
      ],
    );
  }

  Widget _paymentMethods(BuildContext context, TopUpState state) {
    switch (state.fetchPaymentMethodStatus) {
      case AppStatus.initial:
      case AppStatus.loading:
        return _loadingWidget(context);
      case AppStatus.failure:
        return _errorWidget(context);
      case AppStatus.success:
        return PaymentMethodRadioCard(
          display: true,
          selectionCard: state.selectedPaymentMethod?.type ??
              (Platform.isIOS
                  ? CardTypeEnum.apple_pay
                  : CardTypeEnum.google_pay),
          method: state.selectedPaymentMethod,
          onTap: () {
            showPaymentBottomSheet(
              topUpContext: context,
              payments: state.listPayment ?? [],
              selectionPayment: state.selectedPaymentMethod,
              onClickPayment: (payment) => _onClickPayment(payment, context),
            );
            // open sheet payment method
          },
        );
    }
  }

  Widget _errorWidget(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: 300.h,
        minHeight: 300.h,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomTextWidget(
              title: 'error_loading_payment_method'.tr,
              size: 14,
              textAlign: TextAlign.center,
              color: context.black,
            ),
          ],
        ),
      ),
    );
  }

  Widget _emptyWidget(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: 300.h,
        minHeight: 300.h,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: CustomTextWidget(
          size: 14,
          title: 'no_available_payment_method'.tr,
          fontWeight: FontWeight.w600,
          textAlign: TextAlign.center,
          color: context.black,
        ),
      ),
    );
  }

  Widget _loadingWidget(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: context.borderAddBranch,
      highlightColor: context.appBackgroundColor,
      enabled: true,
      child: Row(
        children: [
          // Shimmer for text section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: context.borderAddBranch,
                  ),
                  width: 100.w,
                  height: 12.h,

                  // Placeholder for title
                ),
                SizedBox(height: 4.h),
                Container(
                  width: 60.w,
                  height: 11.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: context.borderAddBranch,
                  ),
                  // Placeholder for subtitle
                ),
              ],
            ),
          ),
          SizedBox(width: 20.w),
          // Shimmer for image/logo
          Container(
            width: 54.w,
            height: 54.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: context.borderAddBranch,
            ),
            // Placeholder for image
          ),
          SizedBox(width: 12.w),
          // Shimmer for selection icon

          Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: context.borderAddBranch,
            ),
            // Placeholder for selection icon
          ),
        ],
      ),
    );
  }

  List<BlocListener> _listeners() {
    return [
      BlocListener<TopUpBloc, TopUpState>(
        listenWhen: (previous, current) =>
            previous.checkOutPaymentStatus != current.checkOutPaymentStatus,
        listener: (context, state) async {
          switch (state.checkOutPaymentStatus) {
            case AppStatus.loading:
              log("lodaing");
              showLoaderDialog(context);
              break;
            case AppStatus.success:
              getIt<IAnalyticsLogger>().logEvent(
                  AnalyticsActions.topUp_checkout); // Analytics action
              dismissLoaderDialog(context);
              if ((state.paymentCheckoutEntity?.clientSecret ?? '')
                      .isNotEmpty &&
                  (state.paymentCheckoutEntity?.publicKey ?? '').isNotEmpty) {
                await payWithMobGeneral(
                  isApplePay: state.selectedPaymentMethod?.type ==
                      CardTypeEnum.apple_pay,
                  clientSecret: state.paymentCheckoutEntity?.clientSecret ?? '',
                  publicKey: state.paymentCheckoutEntity?.publicKey ?? '',
                  savedCards: state.listSavedCards ?? [],
                  context: context,
                  onSuccess: () {
                    reinitializeBloc();
                    Get.offAllNamed(AppRoutes.homePage);
                  },
                  onFailed: (error) {
                    dismissLoaderDialog(context);
                    Get.snackbar("", error.toString());
                  },
                );
              }

              break;
            case AppStatus.failure:
              dismissLoaderDialog(context);
              Get.snackbar("", state.errorMessage ?? "");
              break;
            default:
              break;
          }
        },
      ),
    ];
  }

  void _onClickPayment(PaymentMethodEntity? method, BuildContext context) {
    Get.back();
    if (method != null) {
      context
          .read<TopUpBloc>()
          .add(SelectedPaymentMethodEvent(paymentMethodEntity: method));
    }
  }

  Widget _body(BuildContext context, TopUpState state) {
    final topUp = state.topUp;
    return Expanded(
      child: SingleChildScrollView(
        padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w, top: 12.0.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWidget(
                    title: (topUp?.isInsurancePayment ?? false)
                        ? 'balance_top_up_insurance'.tr
                        : 'balance_top_up'.tr,
                    size: 22, // 13px
                    fontWeight: FontWeight.w600, // 400w
                    color: context.black // 60% opacity
                    ),
                if ((topUp?.isInsurancePayment ?? false).inverted) ...[
                  SizedBox(height: 12.h),
                  CustomTextWidget(
                      title: 'step_1_of_3'.tr,
                      size: 12, // 13px
                      fontWeight: FontWeight.w400, // 400w
                      color: context.lightBlack),
                  SizedBox(height: 40.h),
                  Center(
                    child: CustomTextWidget(
                        title: 'choose_or_enter_the_amount_to_deposit'.tr,
                        size: 13, // 13px
                        fontWeight: FontWeight.w400, // 400w
                        color: context.lightBlack // 60% opacity

                        ),
                  ),
                  SizedBox(height: 40.h),
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          Assets.thrivvePhotosSa,
                          width: 24.w,
                          height: 24.h,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        CustomTextWidget(
                            title: topUp?.currency ?? "sar".tr,
                            textAlign: TextAlign.center,
                            color: context.lightBlack,
                            size: 20,
                            fontWeight: FontWeight.w400),
                        SizedBox(
                          width: 24.w,
                        ),
                        SizedBox(
                          width: 100.w,
                          child: TextField(
                            key: ValueKey('inputTopUpAmount'),
                            textAlign: TextAlign.center,
                            focusNode: _focusNode,
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9]+[,.]{0,1}[0-9]*')),
                            ],
                            controller: textEditingController,
                            textInputAction: TextInputAction.done,
                            decoration: InputDecoration(
                              hintText: '0.00',
                              disabledBorder: UnderlineInputBorder(
                                borderSide:
                                    BorderSide(color: context.whiteColor),
                              ),
                              enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: context.black),
                              ),
                              border: UnderlineInputBorder(
                                borderSide: BorderSide(color: context.black),
                              ),
                              focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: context.black),
                              ),
                              hintStyle: TextStyle(
                                fontSize: 28.0.sp,
                                fontWeight: FontWeight.w600,
                                color: context.black!.withOpacity(0.2),
                              ),
                            ),
                            onChanged: (value) {
                              print("Amount selected: $value");
                              String newText =
                                  convertArabicNumbersToEnglish(value);
                              textEditingController.value = TextEditingValue(
                                text: newText,
                                selection: TextSelection.collapsed(
                                  offset: newText.length,
                                ),
                              );

                              context.read<TopUpBloc>().add(SetTopUpAmount(
                                  amount: newText.isNotEmpty
                                      ? double.tryParse(newText)
                                      : 0.0));
                            },
                            style: TextStyle(
                              fontSize: 28.0.sp,
                              fontWeight: FontWeight.w600,
                              color: context.black,
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 28.h),
                  SizedBox(
                    height: 26.h,
                  ),
                  AmountSelection(
                    amounts: topUp?.suggestedAmounts ?? [],
                    currency: topUp?.currency ?? "sar".tr,
                    onAmountSelected: (amount) {
                      textEditingController.text = amount.toString();
                      context
                          .read<TopUpBloc>()
                          .add(SetTopUpAmount(amount: amount.toDouble()));
                    },
                  ),
                  SizedBox(height: 40.h),
                  titlePayment(context),
                  SizedBox(height: 8.h),
                  _divider(context),
                  SizedBox(height: 8.h),
                  _paymentMethods(context, state),
                  SizedBox(height: 8.h),
                  _divider(context),
                ],
                if ((topUp?.isInsurancePayment ?? false)) ...[
                  SizedBox(
                    height: 40.h,
                  ),
                  Center(
                    child: CustomTextWidget(
                      title: 'amount'.tr,
                      textAlign: TextAlign.center,
                      color: context.black.withValues(alpha: 0.6),
                      size: 13,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(
                    height: 40.h,
                  ),
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          Assets.thrivvePhotosSa,
                          width: 28.w,
                          height: 28.h,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        CustomTextWidget(
                          title: topUp?.currency ?? "sar".tr,
                          textAlign: TextAlign.center,
                          color: context.lightBlack,
                          size: 28,
                          fontWeight: FontWeight.w400,
                        ),
                        SizedBox(
                          width: 24.w,
                        ),
                        StyledDecimalText(
                          decimalNumber: StringUtils.formatCurrency(
                            topUp?.amountTopUp,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 40.h),
                  titlePayment(context),
                  SizedBox(height: 8.h),
                  _divider(context),
                  SizedBox(height: 8.h),
                  _paymentMethods(context, state),
                  SizedBox(height: 8.h),
                  _divider(context),
                ],
                SizedBox(height: 40.h),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> showPaymentBottomSheet(
      {required BuildContext topUpContext,
      required List<PaymentMethodEntity> payments,
      PaymentMethodEntity? selectionPayment,
      required void Function(PaymentMethodEntity paymentEntity)
          onClickPayment}) async {
    final sheet = PaymentMethodsSheet(
      selectedPayment: selectionPayment,
      payments: payments,
      paymentSelection: onClickPayment,
    );
    final input = ShowBottomSheetInput(sheet);
    await Get.find<IShowBottomSheetHelper>().showBottomSheet(input);
  }

  Widget _divider(BuildContext context) {
    return Divider(
      height: 1.h,
      thickness: 1.h,
      color: context.black.withValues(alpha: 0.04),
    );
  }
}
