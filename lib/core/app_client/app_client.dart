import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:get/get.dart' hide Response;
import 'package:thrivve/core/api/api_settings.dart';
import 'package:thrivve/core/controller/language_controller.dart';
import 'package:thrivve/core/error/exceptions.dart';
import 'package:thrivve/core/localDataSource/user_secure_data_source.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/util/general_helper.dart';

enum RequestType {
  post,
  get,
}

class ApiResponse<T> {
  final T? data;
  final int statusCode;
  final String? message;

  ApiResponse({
    required this.data,
    required this.statusCode,
    this.message,
  });
}

class ApiClient {
  final Dio dio;
  final UserSecureDataSource userSecureDataSource;

  ApiClient({
    required this.dio,
    required this.userSecureDataSource,
  });

  // Helper method to remove null values from maps
  Map<String, dynamic> _removeNullValues(Map<String, dynamic> map) {
    return Map.fromEntries(
      map.entries.where((entry) => entry.value != null),
    );
  }

  // Helper method to recursively clean maps and lists
  dynamic _cleanValue(dynamic value) {
    if (value is Map<String, dynamic>) {
      return _removeNullValues(value);
    } else if (value is List) {
      return value.map((item) => _cleanValue(item)).toList();
    }
    return value;
  }

  Future<Map<String, dynamic>> _getHeaders(
      {Map<String, dynamic>? additionalHeaders,
      bool removeNulls = false}) async {
    final defaultHeaders = <String, dynamic>{
      ApiSettings.countryCodeHeader:
          await userSecureDataSource.getCountryCode(),
      ApiSettings.languageHeader:
          Get.find<LanguageController>().currentLanguage.value,
      ApiSettings.deviceFingerprintHeader:
          await userSecureDataSource.getDeviceFingerprint()
    };

    if (additionalHeaders != null) {
      defaultHeaders.addAll(additionalHeaders);
    }

    return removeNulls ? _removeNullValues(defaultHeaders) : defaultHeaders;
  }

  Future<ApiResponse<T>> request<T>({
    required String endpoint,
    required T Function(dynamic) fromJson,
    RequestType method = RequestType.get,
    Object? data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    bool removeNullValues = true,
  }) async {
    try {
      final finalHeaders = await _getHeaders(
          additionalHeaders: headers, removeNulls: removeNullValues);

      // Clean data if it's a map
      Object? cleanedData = data;
      if (removeNullValues && data is Map<String, dynamic>) {
        cleanedData = _cleanValue(data);
      }

      // Clean query parameters
      Map<String, dynamic>? cleanedQueryParameters = queryParameters;
      if (removeNullValues && queryParameters != null) {
        cleanedQueryParameters = _removeNullValues(queryParameters);
      }

      Response response;
      if (method == RequestType.get) {
        response = await dio.get(
          endpoint,
          queryParameters: cleanedQueryParameters,
          options: Options(headers: finalHeaders),
        );
      } else if (method == RequestType.post) {
        response = await dio.post(
          endpoint,
          data: cleanedData,
          queryParameters: cleanedQueryParameters,
          options: Options(headers: finalHeaders),
          onSendProgress: onSendProgress,
          cancelToken: cancelToken,
        );
      } else {
        throw ServerException(
          msj: 'Unsupported HTTP method: $method',
          code: 500,
        );
      }
      if (response.statusCode == 200) {
        log("200 request is here");
        final T result = fromJson(response.data);
        return ApiResponse(
          data: result,
          statusCode: response.statusCode!,
          message: response.statusMessage,
        );
      } else {
        throw ServerException(
          msj: response.statusMessage,
          code: response.statusCode,
        );
      }
    } on DioException catch (ex, stackTrace) {
      final statusCode = ex.response?.statusCode ?? 500;
      final errorMessage =
          getErrorFromErrorList(ex.response?.data) ?? ex.message;

      // Prepare request context for error reporting
      final requestContext = {
        "endpoint": endpoint,
        "method": method.toString(),
        "request_data": data,
        "query_parameters": queryParameters,
        "headers": headers,
        "response_status_code": statusCode,
        "response_data": ex.response?.data,
        "error_message": errorMessage,
        "request_type": method.toString(),
      };

      FirebaseCrashlytics.instance.recordError(
        ex,
        stackTrace,
        reason: "DioException in apiclient line 103",
        information: [requestContext.toString()],
      );

      SentryService.instance.captureException(
        ex,
        stackTrace: stackTrace,
        data: {
          "message": ex.toString(),
          "status_code": statusCode,
          "request_context": requestContext,
        },
      );

      log("❌ DioException in [$method] $endpoint:\nStatus: $statusCode\nMessage: $errorMessage\nStackTrace: $stackTrace\nRequest Context: $requestContext");

      if (ex.response?.statusCode == 401) {
        throw UnauthorisedException(getErrorFromErrorList(ex.response?.data));
      }
      throw ServerException(
        msj: getErrorFromErrorList(ex.response?.data),
        code: ex.response?.statusCode,
      );
    } catch (ex, stackTrace) {
      // Prepare request context for unexpected errors
      final requestContext = {
        "endpoint": endpoint,
        "method": method.toString(),
        "request_data": data,
        "query_parameters": queryParameters,
        "headers": headers,
        "error_message": ex.toString(),
      };

      log("❌ StackTrace: $stackTrace\nRequest Context: $requestContext");

      FirebaseCrashlytics.instance.recordError(
        ex,
        stackTrace,
        reason: "unexpected error in apiclient line 115",
        information: [requestContext.toString()],
      );

      SentryService.instance.captureException(
        ex,
        stackTrace: stackTrace,
        data: {
          "message": ex.toString(),
          "status_code": 500,
          "request_context": requestContext,
        },
      );

      throw ServerException(
        msj: ex.toString(),
        code: 500,
      );
    }
  }
}
