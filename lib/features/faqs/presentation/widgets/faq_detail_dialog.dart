// lib/presentation/widgets/faq_detail_dialog.dart
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_formatted_text.dart';
import 'package:video_player/video_player.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/const.dart';
import '../../../../core/widget/button.dart';
import '../../../onboarding/presentation/widgets/top_bar_widget.dart';
import '../../domain/entities/faq.dart';

class FaqDetailDialog extends StatefulWidget {
  final Faq faq;

  const FaqDetailDialog({super.key, required this.faq});

  @override
  State<FaqDetailDialog> createState() => _FaqDetailDialogState();
}

class _FaqDetailDialogState extends State<FaqDetailDialog> {
  VideoPlayerController? _controller;
  ChewieController? _chewieController;
  bool _isErrorInVideo = false;

  @override
  void initState() {
    if (widget.faq.videoUrl != null) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.faq.videoUrl!),
      )
        ..initialize().then((_) {
          _chewieController = ChewieController(
            videoPlayerController: _controller!,
            autoPlay: false,
            looping: true,
            placeholder: Container(
              color: Colors.grey.shade300,
            ),
          );
          setState(
              () {}); // Ensure the first frame is shown after the video is initialized
        }).catchError((e) {
          // get the error message
          String? error = e is PlatformException ? e.message : e.toString();
          setState(() {
            _isErrorInVideo = true;
          });
          Get.snackbar(
            "err".tr,
            error ?? "error".tr,
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.red.shade400,
            colorText: Get.context!.whiteColor,
            margin: EdgeInsets.all(8.r),
          );
        })
        ..setLooping(false);
    }
    super.initState();
  }

  @override
  void dispose() {
    _controller?.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return faqDetailsWidget(faq: widget.faq);
  }

  Widget faqDetailsWidget({required Faq faq}) {
    return SingleChildScrollView(
      child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: context.bottomsheetColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0.r),
              topRight: Radius.circular(20.0.r),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.max,
            children: [
              const TopBar(),
              SizedBox(height: 28.h), // Responsive height
              Text(
                faq.question,
                // Assuming English is the default language here
                style: TextStyle(
                  fontSize: 15.sp, // Responsive font size
                  fontWeight: FontWeight.w600,
                  color: context.black,
                ),
              ),
              SizedBox(height: 40.h), // Responsive height

              CustomFormattedText(
                text: faq.answer,
                strongStyle: TextStyle(
                  fontSize: 11,
                  height: 1.5,
                  fontFamily: Get.locale?.languageCode == 'en'
                      ? "NotoSans"
                      : "NotoSansArabic",
                  color: context.lightBlack,
                  fontWeight: FontWeight.w400,
                ),
              ),

              SizedBox(height: 24.h),
              getAttachementWidget(faq: faq),

              SizedBox(height: 24.h), // Responsive height
              Button(
                text: "got_it".tr,
                onTab: () {
                  Get.back();
                },
                height: 36.h, // Responsive button height
              )
            ],
          )),
    );
  }

  getAttachementWidget({required Faq faq}) {
    if (faq.attachmentType == attachmentTypeImages) {
      return Column(
        children: [
          HorizontalImageList(imageUrls: faq.imagesUrls!),
        ],
      );
    } else if (faq.attachmentType == attachmentTypeVideo) {
      if (faq.videoUrl != null) {
        return Column(
          children: [
            SizedBox(height: 12.h), // Responsive height
            _controller != null && _controller!.value.isInitialized
                ? SizedBox(
                    height: 196.h, // Responsive height
                    child: Chewie(controller: _chewieController!))
                : _isErrorInVideo == false
                    ? Container(
                        height: 200.h,
                        decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(8.r)),
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      )
                    : Container(), // Responsive height
          ],
        );
      } else {
        return Container();
      }
    } else {
      return Container();
    }
  }
}

class HorizontalImageList extends StatefulWidget {
  final List<String?> imageUrls;

  const HorizontalImageList({super.key, required this.imageUrls});

  @override
  State<HorizontalImageList> createState() => _HorizontalImageListState();
}

class _HorizontalImageListState extends State<HorizontalImageList> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.7);
    _pageController.addListener(() {
      setState(() {
        _currentPage = _pageController.page!.round();
      });
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 400, // Set the height for the image list
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.imageUrls.length,
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {
                  Get.toNamed(
                    AppRoutes.fullScreenImageView,
                    arguments: {
                      'imageUrls': widget.imageUrls,
                      'initialIndex': index,
                    },
                  );
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 8.0.w),
                  // Space between images
                  child: ClipRect(
                    child: Image.network(
                      widget.imageUrls[index] ?? "",
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) {
                          return child;
                        } else {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                      },
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 12.0.h), // Space between images and dots indicator
        // add indicator dots
        _buildDotsIndicator(),
      ],
    );
  }

  Widget _buildDotsIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(widget.imageUrls.length, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4.0),
          width: 8.0,
          height: 8.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                _currentPage == index ? context.appPrimaryColor : Colors.grey,
          ),
        );
      }),
    );
  }
}
