import 'package:thrivve/features/rent_a_car/car_details/data/models/add_on_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/insurance_model.dart';
import 'package:thrivve/features/rent_a_car/car_details/data/models/mileage_model.dart';

class CarRentAdditionalAttributesResponseModel {
  final List<AddOnModel>? _addOns;
  final List<MileageModel>? _mileage;
  final List<InsuranceModel>? _insurance;

  CarRentAdditionalAttributesResponseModel({
    List<AddOnModel>? addOns,
    List<MileageModel>? mileage,
    List<InsuranceModel>? insurance,
  })  : _addOns = addOns,
        _mileage = mileage,
        _insurance = insurance;

  List<AddOnModel>? get addOns => _addOns;
  List<MileageModel>? get mileage => _mileage;
  List<InsuranceModel>? get insurance => _insurance;

  factory CarRentAdditionalAttributesResponseModel.fromJson(
      Map<String, dynamic> json) {
    return CarRentAdditionalAttributesResponseModel(
      addOns: (json['add_ons'] as List<dynamic>?)
          ?.map((e) => AddOnModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      mileage: (json['milage'] as List<dynamic>?)
          ?.map((e) => MileageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      insurance: (json['insurance'] as List<dynamic>?)
          ?.map((e) => InsuranceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'add_ons': _addOns?.map((e) => e.toJson()).toList(),
      'milage': _mileage?.map((e) => e.toJson()).toList(),
      'insurance': _insurance?.map((e) => e.toJson()).toList(),
    };
  }

  CarRentAdditionalAttributesResponseModel copyWith({
    List<AddOnModel>? addOns,
    List<MileageModel>? mileage,
    List<InsuranceModel>? insurance,
  }) {
    return CarRentAdditionalAttributesResponseModel(
      addOns: addOns ?? _addOns,
      mileage: mileage ?? _mileage,
      insurance: insurance ?? _insurance,
    );
  }
}
