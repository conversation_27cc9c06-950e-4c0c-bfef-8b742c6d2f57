import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:thrivve/core/error/failures.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/mappers/link_uber_with_thrivve_response_extension.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/data/models/link_uber_with_thrivve_request_params_model.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/entities/link_uber_with_thrive_response_entity.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/repositories/i_link_uber_with_thrivve_repository.dart';
import 'package:thrivve/features/uber_flow/link_uber_with_thrivve_account/domain/use_cases/link_uber_with_thrivve_use_case.dart';

import '../../data/models/link_uber_with_thrivve_response_test_model.dart';
import 'link_uber_with_thrivve_use_case_test.mocks.dart';

@GenerateMocks([LinkUberWithThrivveRepository])
void main() {
  late LinkUberWithThrivveUseCase useCase;
  late MockLinkUberWithThrivveRepository mockRepository;

  setUp(() {
    mockRepository = MockLinkUberWithThrivveRepository();
    useCase = LinkUberWithThrivveUseCase(mockRepository);
  });

  final params = LinkUberWithThrivveRequestParamsModel(
      // Add necessary parameters here
      );
  final responseEntity = LinkUberWithThrivveResponseTestModel();

  test('should link Uber with Thrivve account successfully', () async {
    // arrange
    when(mockRepository.linkUberWithThrivve(any))
        .thenAnswer((_) async => Right(responseEntity.toEntity()));
    // act
    final result = await useCase.call(params);

    expect(result, isA<Right<Failure, LinkUberWithThrivveResponseEntity?>>());
    expect(result?.getOrElse(() => responseEntity.toEntity()),
        equals(responseEntity.toEntity()));
    verify(mockRepository.linkUberWithThrivve(params));
    verifyNoMoreInteractions(mockRepository);
  });

  test('should return failure when linking fails', () async {
    // arrange
    final failure = ServerFailure();
    when(mockRepository.linkUberWithThrivve(any))
        .thenAnswer((_) async => Left(failure));
    // act
    final result = await useCase(params);
    // assert
    expect(result, Left(failure));
    verify(mockRepository.linkUberWithThrivve(params));
    verifyNoMoreInteractions(mockRepository);
  });
}
