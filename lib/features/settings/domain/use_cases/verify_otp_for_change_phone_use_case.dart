import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../../../onboarding/domain/entities/verify_otp.dart';
import '../repositories/profile_repository.dart';

class VerifyOtpForChangePhoneUseCase implements UseCase<VerifyOtp?, VerifyOptParams> {
  ProfileRepository? profileRepository;

  VerifyOtpForChangePhoneUseCase({this.profileRepository});

  @override
  Future<Either<Failure, VerifyOtp?>?> call(VerifyOptParams params) async {
    return await profileRepository?.verifyOtp(
      mobile: params.mobile,
      otpCode: params.otpCode,
    );
  }
}

class VerifyOptParams {
  String? otpCode;
  String? mobile;

  VerifyOptParams({
    required this.mobile,
    required this.otpCode,
  });
}
