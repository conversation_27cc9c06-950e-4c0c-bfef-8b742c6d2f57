import 'package:dartz/dartz.dart';
import 'package:thrivve/features/topUp/domain/entities/payment_method_entity.dart';
import 'package:thrivve/features/topUp/domain/repositories/top_up_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';

class PaymentMethodsUseCase
    implements UseCase<List<PaymentMethodEntity?>?, NoParams> {
  final TopUpRepository? repository;

  PaymentMethodsUseCase({this.repository});

  @override
  Future<Either<Failure, List<PaymentMethodEntity?>?>?> call(
      NoParams params) async {
    return await repository?.paymentMethods();
  }
}
