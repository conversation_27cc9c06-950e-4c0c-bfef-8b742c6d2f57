import 'package:dartz/dartz.dart';
import 'package:thrivve/features/dashboard/data/mapper/last_progress_extention.dart';
import 'package:thrivve/features/dashboard/data/mapper/new_products_extesnsion.dart';
import 'package:thrivve/features/dashboard/data/mappers/change_language_request_mapper.dart';
import 'package:thrivve/features/dashboard/data/mappers/thrivve_info_mapper.dart';
import 'package:thrivve/features/dashboard/data/models/ClickDriveToOwnResponseModel.dart';
import 'package:thrivve/features/dashboard/data/models/products_model.dart';
import 'package:thrivve/features/dashboard/data/models/work_with_uber_all_data_model.dart';
import 'package:thrivve/features/dashboard/domain/entities/change_language_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/last_progress_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/message_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/new_dashboard_products_entity.dart';
import 'package:thrivve/features/dashboard/domain/entities/sync_notification.dart';
import 'package:thrivve/features/dashboard/domain/entities/thrivve_info_entity.dart';
import 'package:thrivve/features/personalInfo/data/mappers/personal_info_mapper.dart';
import 'package:thrivve/features/personalInfo/domain/entities/personal_info.dart';
import 'package:thrivve/features/transactions/domain/entities/transactions_filter.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../../invoices/domain/entities/invoice_filter.dart';
import '../../../invoices/domain/entities/invoice_v2.dart';
import '../../domain/entities/banners.dart';
import '../../domain/entities/insights.dart';
import '../../domain/entities/main_transaction.dart';
import '../../domain/entities/payment_method.dart';
import '../../domain/entities/withdraw_balance.dart';
import '../../domain/entities/withdraw_config.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../data_sources/dashboard_remote_data_source.dart';

class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource remoteDataSource;

  final NetworkInfo networkInfo;

  DashboardRepositoryImpl(
      {required this.remoteDataSource, required this.networkInfo});

  @override
  Future<Either<Failure, ThrivveInfoEntity?>> getInfoThrivveData() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getInfoThrivveData();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, WithdrawBalance?>>? getWithdrawBalance() async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getWithdrawBalance());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PersonalInfoEntity?>>? getPersonalInfo() async {
    if (await networkInfo.isConnected!) {
      try {
        final result = await remoteDataSource.getPersonalInfo();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<PaymentMethod>?>>? getPaymentMethods(
      {String? paymentType}) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(
            await remoteDataSource.getPaymentMethods(paymentType: paymentType));
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<Banners>?>>? getBanners() async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getBanners());
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, Insights?>>? getInsights() async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getInsights());
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, WithdrawConfig?>>? getWithdrawConfig() async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getWithdrawConfig());
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, double?>>? getWithdrawFees(
      {required double? amount}) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getWithdrawFees(amount: amount));
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, MainTransaction?>>? getTransactions({
    String? type,
    String? status,
    required int? page,
    required int? perPage,
    String? startDate,
    String? endDate,
    String? searchText,
  }) async {
    if (await networkInfo.isConnected!) {
      try {
        return Right(await remoteDataSource.getTransactions(
          type: type,
          page: page,
          status: status,
          perPage: perPage,
          startDate: startDate,
          endDate: endDate,
          searchText: searchText,
        ));
      } on ServerException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      } on NetworkException catch (msj) {
        return left(ServerFailure(msj: msj.msj));
      }
    } else {
      return left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, ChangeLanguageEntity?>?>? changeLanguage(
      {required String? language}) async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.changeLanguage(
          language: language,
        );
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String?>>? deleteUserAccount() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.deleteUserAccount());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, ProductsModel?>>? getProducts(
      {bool? isPublic, int? page, int? perPage}) async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getProducts(
            isPublic: isPublic, page: page, perPage: perPage));
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<InvoiceV2>?>?>? getInvoices({
    String? filterId,
  }) async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getInvoices(status: filterId));
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<InvoiceFilter?>?>?>? getListOfFilter() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getListOfFilter());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String?>>? getSupportUrl() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getSupportUrl());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<TransactionsFilter?>?>?>?
      getListOfTransactionsFilter() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getListOfTransactionsFilter());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, SyncNotification?>>? getSyncInfo() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getSyncInfo());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, String?>>? setSyncInfoSeen() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.setSyncInfoSeen());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, MessageEntity?>?>? getHomePageMessage() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getHomePageMessage());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, WorkWithUberAllDataModel?>>?
      getWorkWithUberAllData() async {
    if (await networkInfo.isConnected == true) {
      try {
        return Right(await remoteDataSource.getWorkWithUberAllData());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, LastProgressEntity?>?> getLastProgress() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getLastProgress();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, NewDashboardProductsEntity?>?>
      getNewDashboardProducts() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.getNewDashboardProducts();
        return Right(result?.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, ClickDriveToOwnResponseModel?>?>
      clickDriveToOwn() async {
    if (await networkInfo.isConnected == true) {
      try {
        final result = await remoteDataSource.clickDriveToOwn();
        return Right(result);
      } on ServerException catch (e) {
        return Left(ServerFailure(msj: e.msj));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(msj: e.msj));
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
