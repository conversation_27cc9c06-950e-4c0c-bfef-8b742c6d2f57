import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';

class DateFilterTag extends StatelessWidget {
  final String text;
  final Color? color;
  final Color? textColor;
  final bool isSelected;
  final Function(bool) onSelect;

  const DateFilterTag({
    super.key,
    required this.text,
    this.color,
    this.textColor,
    this.isSelected = false,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onSelect(!isSelected),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        // Responsive padding
        decoration: BoxDecoration(
          color: isSelected ? context.appPrimaryColor : context.containerColor,
          borderRadius: BorderRadius.circular(16.r), // Responsive radius
          border: Border.all(
            color:
                isSelected ? context.appPrimaryColor : context.iconBoarderColor,
          ),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomTextWidget(
                title: text,
                color: isSelected ? Colors.white : (textColor ?? context.black),
                size: 10, // Responsive font size
                fontWeight: FontWeight.w500,
              ),
              // If selected, show icon with close else show empty container
              isSelected
                  ? Row(
                      children: [
                        SizedBox(width: 4.w), // Responsive width
                        InkWell(
                          onTap: () => onSelect(false),
                          child: Container(
                            height: 14.h, // Responsive height
                            width: 14.w, // Responsive width
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(90.r),
                              // Responsive radius
                              border: Border.all(
                                color: Colors.white,
                              ),
                            ),
                            child: Icon(
                              Icons.close,
                              size: 10.sp, // Responsive size
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    )
                  : SizedBox(width: 0.w), // Responsive width
            ],
          ),
        ),
      ),
    );
  }
}
