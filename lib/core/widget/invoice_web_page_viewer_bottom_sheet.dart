import 'dart:async';
import 'dart:io';
import 'package:app_settings/app_settings.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import 'package:thrivve/core/widget/app_loading_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

class InvoiceWebPageViewerBottomSheet extends StatefulWidget {
  final String? url;
  final String? downloadUrl;

  const InvoiceWebPageViewerBottomSheet(
      {super.key, this.url, this.downloadUrl});

  @override
  State<InvoiceWebPageViewerBottomSheet> createState() =>
      _InvoiceWebPageViewerBottomSheetState();
}

class _InvoiceWebPageViewerBottomSheetState
    extends State<InvoiceWebPageViewerBottomSheet> {
  String? title;
  bool isLoading = true;

  final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers = {
    Factory(() => EagerGestureRecognizer())
  };

  final UniqueKey _key = UniqueKey();

  late WebViewController _controller;

  var downloading = false;
  var icon = Icons.download;
  var error = '';

  @override
  void initState() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..addJavaScriptChannel("Flutter", onMessageReceived: (message) {
        String pageBody = message.message;
        if (kDebugMode) {
          print('page body: $pageBody');
        }
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (progress == 100) {
              setState(() {
                isLoading = false;
              });
            }
            if (kDebugMode) {
              print('WebView is loading (progress : $progress%)');
            }
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
            });
            if (kDebugMode) {
              print('Page started loading: $url');
            }
          },
          onPageFinished: (String url) async {
            String script = 'window.document.title';
            String? title = (await _controller
                .runJavaScriptReturningResult(script)) as String?;
            String? result = title?.replaceAll(RegExp('["]'), '');
            if (kDebugMode) {
              print('Page finished loading: $result');
            }
            setState(() {
              this.title = result;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url ?? ""));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
          color: context.containerColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0.0,
          title: Text(
            title ?? "",
            style: TextStyle(
              color: context.black,
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          actions: [
            downloading
                ? Container(
                    width: 50,
                    height: 50,
                    padding: const EdgeInsets.all(8),
                    child: const CircularProgressIndicator())
                : IconButton(
                    icon: Icon(icon),
                    onPressed: () {
                      requestPermission(url: widget.downloadUrl ?? "");
                    },
                  ),
          ],
        ),
        body: Container(
          padding: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
              color: context.containerColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.0),
                  topRight: Radius.circular(16.0))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              error.isNotEmpty
                  ? Expanded(
                      child: Container(
                        height: 40,
                        color: Colors.red,
                        child: Text(
                          error,
                          style: TextStyle(
                            color: context.whiteColor,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
              Expanded(
                child: Stack(
                  children: [
                    WebViewWidget(
                      key: _key,
                      gestureRecognizers: gestureRecognizers,
                      controller: _controller,
                    ),
                    Visibility(
                        visible: isLoading, child: const AppLoadingWidget())
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> requestPermission({required String url}) async {
    try {
      final status = await Permission.storage.request();
      PermissionStatus permissionStatus = status;
      if (permissionStatus.isGranted) {
        openFile(url);
      } else if (permissionStatus.isDenied) {
      } else if (permissionStatus.isPermanentlyDenied) {
        AppSettings.openAppSettings();
      }
    } on PlatformException catch (e) {
      if (e.code == 'PERMISSION_DENIED') {
        error = "Could not get permission from the system to open the file";
      } else if (e.code == 'PERMISSION_DENIED_NEVER_ASK') {
        error =
            "Permission Denied - ask the user to enable it from the app settings";
      }
      if (Platform.isIOS) {
        if (kDebugMode) {
          print("Could not get permission from the system to open the file");
        }
      }
    } catch (_) {
      if (Platform.isIOS) {
        if (kDebugMode) {
          print("Could not get permission from the system to open the file");
        }
      }
      return;
    }
  }

  void openFile(String url) async {
    String? dir;
    if (Platform.isAndroid) {
      dir = (await getExternalStorageDirectory())?.path;
    } else {
      dir = (await getApplicationDocumentsDirectory()).path;
    }
    var filePath = "$dir/${url.substring(url.lastIndexOf('/') + 1)}";

    File file = File(filePath);
    var isExist = await file.exists();
    if (isExist) {
      await OpenFile.open(filePath);
    } else {
      downloadFile(url, filePath);
    }
  }

  Future<void> downloadFile(String url, String filePath) async {
    Dio dio = Dio();
    var titleTemp = title;
    setState(() {
      title = "0%";
      downloading = true;
    });

    try {
      await dio.download(url, filePath, onReceiveProgress: (
        rec,
        total,
      ) {
        setState(() {
          title = "${((rec / total) * 100).toStringAsFixed(0)}%";
        });
      });
    } catch (e) {
      Get.snackbar("Error", "Error downloading file from server",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.8),
          colorText: Get.context!.whiteColor,
          margin: const EdgeInsets.all(10),
          borderRadius: 10,
          duration: const Duration(seconds: 2));
      if (kDebugMode) {
        print(e);
      }
    }

    setState(() {
      downloading = false;
      icon = Icons.file_open;
      title = titleTemp;
    });
  }
}
