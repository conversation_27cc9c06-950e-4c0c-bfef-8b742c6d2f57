import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';
import 'package:thrivve/core/widget/custom_text_widget.dart';
import 'package:thrivve/features/mainHome/presentation/manager/main_home_bloc.dart';
import 'package:thrivve/features/under_processing/domain/entities/under_processing_entity.dart';
import 'package:thrivve/features/under_processing/domain/enums/entry_type_enum.dart';
import 'package:thrivve/features/withdraw/presentation/widgets/custom_button.dart';

import '../../../../core/app_routes.dart';
import '../../../../core/util/app_status.dart';
import '../../../../generated/assets.dart';
import '../manager/dashboard_bloc.dart';

class UnderProcessingWidget extends StatelessWidget {
  const UnderProcessingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      builder: (context, state) {
        switch (state.getUnderProcessingStatus) {
          case AppStatus.loading:
            return _lazyLoadingUnderProcessing();
          case AppStatus.success:
            return _successLoadedUnderProcessingList(
                state.listUnderProcessing, context);
          default:
            return SizedBox.shrink();
        }
      },
    );
  }

  Widget _contentUnderProcessing(
      List<UnderProcessingEntity>? listUnderProcessing, BuildContext context) {
    final itemCount = (listUnderProcessing ?? []).length;
    if (itemCount > 1) {
      return _itemProcessing(
        title: ('requests_under_processing'.tr)
            .replaceAll('#', (itemCount ?? 0).toString()),
        subTitle: 'processing_your_request'.tr,
        icon: Assets.assetsThrivvePhotosUnderProccessingIcon,
        onClickViewDetails: () => _onClickShowUnderProcessingPage(context),
      );
    } else if (itemCount == 1) {
      final item = listUnderProcessing?.firstOrNull;
      return _itemProcessing(
        title: '${getTitle(item?.entryType)} (${item?.amountAbs ?? ''})',
        subTitle: 'processing_your_request'.tr,
        icon: item?.icon ?? 'in',
        onClickViewDetails: () => _onClickViewDetails(item, context),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  String getTitle(EntryTypeEnum? type) {
    if (type == null) return 'top_up_request'.tr;
    switch (type) {
      case EntryTypeEnum.paymentRequest:
        return 'withdraw_request'.tr;
      case EntryTypeEnum.depositAccount:
        return 'top_up_request'.tr;
      case EntryTypeEnum.currentAccount:
        return 'top_up_request'.tr;
    }
  }

  Widget _itemProcessing(
      {required String title,
      required String subTitle,
      required String icon,
      required VoidCallback onClickViewDetails}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onClickViewDetails,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              children: [
                _image(icon),
                SizedBox(width: 8.w),
                _titleAndSub(title, subTitle),
                SizedBox(width: 5.w),
              ],
            ),
          ),
          CustomButton(
            padding: EdgeInsetsDirectional.symmetric(
              horizontal: 12.sp,
              vertical: 7.sp,
            ),
            label: 'view_details'.tr,
            isEnabled: true,
            onPressed: onClickViewDetails,
          )
        ],
      ),
    );
  }

  bool isUberOrAmazon(String? iconType) {
    return iconType == "uber_in" || iconType == "amazon_in";
  }

  String getIcon(String? iconType) {
    switch (iconType) {
      case "out":
        return Assets.thrivvePhotosWithdrawIcon;
      case "invoice_out":
        return Assets.thrivvePhotosInvoices;
      case "in":
        return Assets.thrivvePhotosRevenueIcon;
      case "cash_out":
        return Assets.thrivvePhotosCashFromUberIcon;
      case "uber_in":
        return Assets.thrivvePhotosUberIcon;
      case "under_processing":
        return Assets.thrivveVideosLoading;
      case "amazon_in":
        return Assets.thrivvePhotosAmazonIcon;
      default:
        return Assets.thrivveVideosLoading;
    }
  }

  Widget _image(String icon) {
    return isUberOrAmazon(icon)
        ? Container(
            alignment: Alignment.center,
            height: 40.h,
            width: 40.w,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: Image.asset(
              getIcon(icon),
            ),
          )
        : Container(
            alignment: Alignment.center,
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Get.context!.appBackgroundColor,
            ),
            child: Image.asset(
              getIcon(icon),
              width: 20.w,
              height: 20.h,
              color: Get.context!.black,
            ),
          );
  }

  Widget _titleAndSub(String title, String subTitle) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomTextWidget(
            size: 12,
            fontWeight: FontWeight.w700,
            title: title,
            color: Get.context!.black,
          ),
          SizedBox(height: 4.sp),
          CustomTextWidget(
            title: subTitle,
            size: 12,
            color: Get.context!.lightBlack,
          )
        ],
      ),
    );
  }

  Widget _lazyLoadingUnderProcessing() {
    return Padding(
      padding: EdgeInsetsDirectional.only(
        top: 40.sp,
        start: 16.w,
        end: 16.w,
      ),
      child: Shimmer.fromColors(
        baseColor: Get.context!.borderAddBranch!,
        highlightColor: Get.context!.appBackgroundColor!,
        enabled: true,
        child: _itemProcessing(
          title: "**********",
          subTitle: "######",
          icon: Assets.assetsThrivvePhotosUnderProccessingIcon,
          onClickViewDetails: () {},
        ),
      ),
    );
  }

  Widget _successLoadedUnderProcessingList(
      List<UnderProcessingEntity>? listUnderProcessing, BuildContext context) {
    if ((listUnderProcessing ?? []).isEmpty) return SizedBox.shrink();
    return Padding(
      padding: EdgeInsetsDirectional.only(
        top: 40.sp,
        start: 16.w,
        end: 16.w,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomTextWidget(
            title: 'pending_requests'.tr,
            fontWeight: FontWeight.w600,
            color: context.black!,
            size: 16,
          ),
          Container(
            margin: EdgeInsetsDirectional.only(top: 16.sp),
            padding: EdgeInsetsDirectional.all(12.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.sp),
              border: Border.all(
                color: context.borderColor!,
              ),
            ),
            child: _contentUnderProcessing(listUnderProcessing, context),
          ),
        ],
      ),
    );
  }

  void _onClickShowUnderProcessingPage(BuildContext context) {
    Get.toNamed(
      AppRoutes.underProcessingPage,
    )?.then((value) {
      _refreshDataDashBoard(context);
    });
  }

  void _onClickViewDetails(
    UnderProcessingEntity? item,
    BuildContext context,
  ) {
    if (item?.referenceId != null) {
      Get.toNamed(
        (item?.isTransaction ?? false)
            ? AppRoutes.transactionDetailsPage
            : AppRoutes.pendingTransactionDetailsPage,
        arguments: item?.referenceId,
      )?.then((value) {
        _refreshDataDashBoard(context);
      });
    }
  }

  void _refreshDataDashBoard(BuildContext context) {
    context.read<MainHomeBloc>().add(const GetBalanceEvent());
    context.read<DashboardBloc>().add(GetWidgetNotificationEvent());
    context.read<DashboardBloc>().add(GetListOfTransactionsEvent());
    context.read<DashboardBloc>().add(GetUnderProcessingListEvent());
  }
}
