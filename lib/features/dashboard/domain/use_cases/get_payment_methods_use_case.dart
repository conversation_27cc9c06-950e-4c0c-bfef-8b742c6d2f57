import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/user_cases/user_case.dart';
import '../entities/payment_method.dart';
import '../repositories/dashboard_repository.dart';

class GetPaymentMethodsUseCase
    implements UseCase<List<PaymentMethod>?, GetPaymentMethodParams> {
  final DashboardRepository? dashboardRepository;

  GetPaymentMethodsUseCase({this.dashboardRepository});

  @override
  Future<Either<Failure, List<PaymentMethod>?>?> call(
      GetPaymentMethodParams params) async {
    return await dashboardRepository?.getPaymentMethods(
        paymentType: params.paymentType);
  }


}

class GetPaymentMethodParams {
  String? paymentType;

  GetPaymentMethodParams({this.paymentType});

}
