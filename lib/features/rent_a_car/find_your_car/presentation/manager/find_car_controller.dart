import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/app_routes.dart';
import 'package:thrivve/core/extentions/safe_cast.dart';
import 'package:thrivve/core/services/sentry_service.dart';
import 'package:thrivve/core/user_cases/user_case.dart';
import 'package:thrivve/core/util/general_helper.dart';
import 'package:thrivve/core/util/helper.dart';
import 'package:thrivve/core/util/search_debouncer.dart';
import 'package:thrivve/features/rent_a_car/car_details/presentation/arguments/vehicle_details_arguments.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/entities/lease_vehicle_entity.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/entities/sort_option_entity.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/use_cases/fetch_cars_use_case.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/domain/use_cases/get_sort_options_use_case.dart';
import 'package:thrivve/features/rent_a_car/find_your_car/presentation/arguments/find_your_car_arguments.dart';

class FindCarController extends GetxController {
  final GetAllLeaseVehiclesUseCase getAllLeaseVehiclesUseCase;
  final GetSortOptionsUseCase getSortOptionsUseCase;
  FindCarController(
      this.getAllLeaseVehiclesUseCase, this.getSortOptionsUseCase);

  final TextEditingController searchController = TextEditingController();
  Rx<int> selectedFilterIndex = 0.obs;

  Rx<String> selectedOption = "".obs;
  Rx<String> selectedSortBy = "".obs;
  RxBool showClearButton = false.obs;
  Timer? _debounce;
  final vehicleSelectionId = Rx<int?>(null);
  final vehicleItemSelection = Rx<LeaseVihecleEntity?>(null);
  final ScrollController scrollController = ScrollController();

  RxList<LeaseVihecleEntity> vehicles = <LeaseVihecleEntity>[].obs;
  RxList<SortOptionEntity> sortOptions = <SortOptionEntity>[].obs;
  int page = 1;
  RxBool isLoading = false.obs;
  RxBool isSortOptionsLoading = false.obs;

  final _arguments = Rx<FindYourCarArguments?>(null);
  FindYourCarArguments? get arguments => _arguments.value;
  set arguments(FindYourCarArguments? newValue) => _arguments.value = newValue;

  RxInt targetScrollIndex = RxInt(-1);

  void clearSearch() {
    _logUserInteraction('clear_search', data: {
      'previous_search': searchController.text,
      'vehicles_count': vehicles.length,
    });
    searchController.clear();

    page = 1;
    vehicles.clear();
    _getLeaseVehicles();
  }

  final _reloadDebouncer = Debouncer(milliseconds: 700);
  void onSearchChanged(String query) {
    _logUserInteraction('search_changed', data: {
      'query': query,
      'is_empty': query.isEmpty,
    });
    _reloadDebouncer.run(() => _performSearch(query));
  }

  void _performSearch(String query) {
    _logUserInteraction('perform_search', data: {
      'query': query,
      'page': page,
    });
    page = 1;
    vehicles.clear();
    _getLeaseVehicles();
  }

  RxBool isInitialLoading = false.obs;
  void _getLeaseVehicles() async {
    if (isLoading.value) return;

    if ((vehicles.length % 10) != 0) {
      return;
    }

    if (page == 1) {
      isInitialLoading.value = true;
      vehicles.clear(); // Clear vehicles only on first page
    }

    isLoading.value = true;

    // Only scroll to end if page!=1
    if (page != 1) {
      _scrollToEnd();
    }

    final result = await getAllLeaseVehiclesUseCase.call(
      LeaseVehiclesUseCaseParams(
        page: page,
        searchWord: searchController.text,
        sortBy: selectedSortBy.value.isNotEmpty ? selectedSortBy.value : null,
      ),
    );

    isInitialLoading.value = false;
    result.fold(
      (failure) {
        isLoading.value = false;
        final error = mapFailureToMessage(failure);
        errorSnackBar(context: Get.context!, title: "err".tr, message: error);
      },
      (list) async {
        isLoading.value = false;
        page++;

        vehicles.addAll(list ?? []);
      },
    );
  }

  _getSortOptions() async {
    isSortOptionsLoading.value = true;

    final result = await getSortOptionsUseCase.call(NoParams());

    result.fold(
      (failure) {
        isSortOptionsLoading.value = false;
        final error = mapFailureToMessage(failure);
        errorSnackBar(context: Get.context!, title: "err".tr, message: error);
      },
      (list) {
        isSortOptionsLoading.value = false;
        if (list != null) {
          sortOptions.assignAll(list);

          // Find and set the default option
          final defaultOption = list.firstWhere(
            (option) => option.isDefault == true,
            orElse: () => list.isNotEmpty ? list.first : SortOptionEntity(),
          );

          if (defaultOption.value != null && defaultOption.title != null) {
            selectedSortBy.value = defaultOption.value!;
            selectedOption.value = defaultOption.title!;

            // Find the index of the default option for UI selection
            final defaultIndex =
                list.indexWhere((option) => option.isDefault == true);
            if (defaultIndex != -1) {
              selectedFilterIndex.value = defaultIndex;
            }
          }
        }
      },
    );
  }

  // Method to update sort option and refresh vehicles list
  void updateSortOption(String sortByValue, String displayTitle) {
    _logUserInteraction('update_sort_option', data: {
      'sort_by': sortByValue,
      'display_title': displayTitle,
    });

    selectedSortBy.value = sortByValue;
    selectedOption.value = displayTitle;

    // Reset pagination and refresh vehicles list
    page = 1;
    vehicles.clear();
    _getLeaseVehicles();
  }

  @override
  void onInit() {
    super.onInit();
    arguments = safeCast<FindYourCarArguments>(Get.arguments);
    _startJourneySentry();
    _startChild();
    _getLeaseVehicles();
    _getSortOptions();
    getArguments();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollController.jumpTo(0); // Ensures the scroll position is at the top
    });
    scrollController.addListener(() {
      final maxScrollExtent = scrollController.position.maxScrollExtent;
      final offset = scrollController.offset;
      final outOfRange = scrollController.position.outOfRange;
      if (offset >= maxScrollExtent && !outOfRange) {
        _getLeaseVehicles();
      }
    });

    searchController.addListener(() {
      if (searchController.text.trim().isNotEmpty) {
        showClearButton.value = true;
        onSearchChanged(searchController.text);
      } else {
        showClearButton.value = false;
      }
    });
  }

  @override
  void onClose() {
    _debounce?.cancel();

    scrollController.dispose();
    super.onClose();
  }

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Map<String, dynamic>? dlParams;
  getArguments() {
    arguments = safeCast<FindYourCarArguments>(Get.arguments);
    dlParams = arguments?.dlParams;
  }

  bool stopSearchForScroll = false;

  navigationMethod(VehicleDetailsArguments args) async {
    _logUserInteraction('navigate_to_vehicle_details', data: {
      'vehicle_id': args.vehicleId,
      'has_dl_params': dlParams != null,
    });

    // Start child span for car selection

    try {
      vehicleSelectionId.value = args.vehicleId;

      // Add breadcrumb for car selection
      _logUserInteraction(
        'user_selected_car',
        data: {
          'vehicle_id': args.vehicleId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Navigate to vehicle details
      Get.toNamed(Routes.rentVehicleDetailsPage, arguments: args);
      // Add breadcrumb for successful navigation
    } catch (e, stackTrace) {
      // Log any errors during navigation
      SentryService.instance.captureException(
        e,
        stackTrace: stackTrace,
        data: {
          'vehicle_id': args.vehicleId,
          'action': 'navigation',
        },
      );
    } finally {
      // Finish the child span
      _finishChild();
    }
  }

  void _startJourneySentry() {
    SentryService.instance.startTransaction(
      "rent_car_flow",
      "rent_car_flow.startFlow",
    );
  }

  void _logUserInteraction(String action, {Map<String, dynamic>? data}) {
    SentryService.instance.addBreadcrumb(
      message: 'FindCar: $action',
      category: 'Find_Car'.toLowerCase(),
      data: {
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        if (data != null) ...data,
      },
    );
  }

  void _startChild() {
    SentryService.instance.startChildSpan(
      'CarSelection'.toUpperCase(),
      'FindCar.SelectCar',
    );
  }

  void _finishChild() {
    SentryService.instance.finishSpan('CarSelection');
  }
}
