import 'dart:convert';

/// message : "Token refreshed successfully"
/// access_token : "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c4lLjImw_N_x9lLsPVMnknu2Um8yRCnxktwy0XoFxa8"
/// refresh_token : "7f307cfc-9645-49d0-a4c6-c71e342300c7"

RefreshTokenModel refreshTokenModelFromJson(String str) =>
    RefreshTokenModel.fromJson(json.decode(str));
String refreshTokenModelToJson(RefreshTokenModel data) =>
    json.encode(data.toJson());

class RefreshTokenModel {
  RefreshTokenModel({
    String? message,
    String? accessToken,
    String? refreshToken,
  }) {
    _message = message;
    _accessToken = accessToken;
    _refreshToken = refreshToken;
  }

  RefreshTokenModel.fromJson(dynamic json) {
    _message = json['message'];
    _accessToken = json['access_token'];
    _refreshToken = json['refresh_token'];
  }
  String? _message;
  String? _accessToken;
  String? _refreshToken;
  RefreshTokenModel copyWith({
    String? message,
    String? accessToken,
    String? refreshToken,
  }) =>
      RefreshTokenModel(
        message: message ?? _message,
        accessToken: accessToken ?? _accessToken,
        refreshToken: refreshToken ?? _refreshToken,
      );
  String? get message => _message;
  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['message'] = _message;
    map['access_token'] = _accessToken;
    map['refresh_token'] = _refreshToken;
    return map;
  }
}
