def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    namespace "com.app.thrivve.finance.thrivve"
    compileSdkVersion 35
    ndkVersion "27.0.12077973"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.app.thrivve.finance.thrivve"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "app"

    productFlavors {
        dev {
            dimension "app"
            resValue "string", "app_name", "Thrivve Dev ثرايف"
            applicationId "com.app.thrivve.finance.thrivve.dev"
        }
        stg {
            dimension "app"
            resValue "string", "app_name", "Thrivve Stage"
            applicationId "com.app.thrivve.finance.thrivve.stg"
        }
        prod {
            dimension "app"
            resValue "string", "app_name", "Thrivve ثرايف"
            applicationId "com.app.thrivve.finance.thrivve"
        }
    }

    // Enable data binding as per Paymob instructions
    buildFeatures {
        dataBinding = true
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.2'
    implementation 'androidx.window:window:1.3.0'
    implementation 'androidx.window:window-java:1.3.0'
    implementation 'com.google.android.material:material:1.9.0'
    // Add Paymob SDK dependency
    implementation("com.paymob.sdk:Paymob-SDK:1.5.9") // Ensure version matches your downloaded SDK
}
