
class ApplyRentCarModel {
  final String? _title;
  final String? _body;

  ApplyRentCarModel({
    String? title,
    String? body,
  })  : _title = title,
        _body = body;

  String? get title => _title;
  String? get body => _body;

  factory ApplyRentCarModel.fromJson(Map<String, dynamic> json) {
    return ApplyRentCarModel(
      title: json['titile'] as String?,
      body: json['body'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'titile': _title,
      'body': _body,
    };
  }

  ApplyRentCarModel copyWith({
    String? title,
    String? body,
  }) {
    return ApplyRentCarModel(
      title: title ?? _title,
      body: body ?? _body,
    );
  }
}
