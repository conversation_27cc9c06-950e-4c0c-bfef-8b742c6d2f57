import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:thrivve/core/extentions/optional_mapper.dart';

import '../../../../generated/assets.dart';

class MaintenanceWidget extends StatelessWidget {
  final String? maintenanceReason;
  final String? maintenanceDistance;

  const MaintenanceWidget(
      {super.key, this.maintenanceDistance, this.maintenanceReason});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          color: context.whiteColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("maintenance".tr,
                style: TextStyle(
                    fontFamily: 'cairo',
                    color: context.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w500)),
            const SizedBox(
              height: 12,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Image.asset(
                      Assets.thrivvePhotosOilChange,
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "reason".tr,
                          style: TextStyle(
                              fontFamily: 'cairo',
                              color: context?.statusBackground,
                              fontSize: 12,
                              fontWeight: FontWeight.w500),
                        ),
                        Text(maintenanceReason ?? "",
                            style: TextStyle(
                                fontFamily: 'cairo',
                                color: context.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w600)),
                      ],
                    )
                  ],
                ),
                Row(
                  children: [
                    Image.asset(
                      Assets.thrivvePhotosDistance,
                      width: 24,
                      height: 24,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "distance".tr,
                          style: TextStyle(
                              fontFamily: 'cairo',
                              color: context?.statusBackground,
                              fontSize: 12,
                              fontWeight: FontWeight.w500),
                        ),
                        Text(
                          maintenanceDistance ?? "",
                          style: TextStyle(
                              fontFamily: 'cairo',
                              color: context?.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w600),
                        ),
                      ],
                    )
                  ],
                )
              ],
            )
          ],
        ));
  }
}
