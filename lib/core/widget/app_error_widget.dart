import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppErrorWidget extends StatelessWidget {
  final String error;

  final Function() onPress;

  const AppErrorWidget({super.key, required this.error, required this.onPress});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "Error ${error.toString()}",
          textAlign: TextAlign.center,
        ),
        TextButton(
            onPressed: onPress,
            child: Text(
              "reload".tr,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ))
      ],
    );
  }
}
